# 智能配电箱微信小程序

基于uni-app开发的智能配电箱监控系统微信小程序，包含数据统计、图表展示、设备管理等功能。

## 项目特点

- 基于uni-app框架开发，支持多端部署
- 当前仅支持微信小程序版本
- 包含多种数据图表展示（柱状图、折线图、饼图）
- 支持租户选择和数据筛选

## 功能模块

1. **首页（数据统计）**
   - 租户选择
   - 设备数量统计
   - 用电量统计
   - 预警报警列表
   - 用电量趋势图
   - 巡检进度
   - 设备分布统计

2. **设备列表**
   - 设备状态展示
   - 电压、电流、功率数据
   - 设备管理功能

## 开发环境配置

### 环境要求

- Node.js 12.0+
- yarn 或 npm
- 微信开发者工具

### 项目安装

```bash
# 使用yarn
yarn install

# 或使用npm
npm install
```

## 运行步骤

### 微信小程序开发模式

```bash
# 编译微信小程序（开发模式）
npm run dev:mp-weixin

# 或使用yarn
yarn dev:mp-weixin
```

编译完成后，打开微信开发者工具：

1. 点击工具栏中的【项目】->【导入项目】
2. 选择项目目录中的 `dist/dev/mp-weixin` 文件夹
3. 导入后即可预览运行

### 生产环境打包

```bash
# 编译微信小程序（生产模式）
npm run build:mp-weixin

# 或使用yarn
yarn build:mp-weixin
```

打包完成后，使用微信开发者工具导入 `dist/build/mp-weixin` 文件夹即可上传发布。

## 注意事项

1. 当前项目仅支持微信小程序版本
2. 使用前需在 `src/manifest.json` 中配置您的微信小程序 AppID
3. 项目中的数据均为模拟数据，实际使用时需要对接真实的后端接口

## 相关文档

- [uni-app 官方文档](https://uniapp.dcloud.io/)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
