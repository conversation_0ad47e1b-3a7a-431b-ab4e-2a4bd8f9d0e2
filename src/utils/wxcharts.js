/*
 * wx-charts.js
 *
 * 这是一个简化版的 wx-charts.js 文件，用于在微信小程序中绘制图表。
 * 完整版请访问：https://github.com/xiaolin3303/wx-charts
 */

var config = {
    yAxisWidth: 15,
    yAxisSplit: 5,
    xAxisHeight: 15,
    xAxisLineHeight: 15,
    legendHeight: 15,
    yAxisTitleWidth: 15,
    padding: 12,
    columePadding: 3,
    fontSize: 10,
    dataPointShape: ['circle', 'diamond', 'triangle', 'rect'],
    colors: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
    pieChartLinePadding: 25,
    pieChartTextPadding: 15,
    xAxisTextPadding: 3,
    titleColor: '#333333',
    titleFontSize: 20,
    subtitleColor: '#999999',
    subtitleFontSize: 15,
    toolTipPadding: 3,
    toolTipBackground: '#000000',
    toolTipOpacity: 0.7,
    toolTipLineHeight: 14,
    radarGridCount: 3,
    radarLabelTextMargin: 15
};

// 支持的图表类型
var CHART_TYPES = {
    LINE: 'line',
    COLUMN: 'column',
    PIE: 'pie',
    RING: 'ring',
    AREA: 'area',
    RADAR: 'radar'
};

// 格式化数字，保留指定位数的小数
function formatNumber(num, digit = 2) {
    return parseFloat(num.toFixed(digit));
}

// 创建图表
function create(opts) {
    var chartType = opts.type || 'column';
    var ctx = opts.context;
    var canvasId = opts.canvasId;

    if (!ctx) {
        throw new Error('No canvas context specified');
    }

    // 根据图表类型创建不同的图表
    switch (chartType) {
        case CHART_TYPES.LINE:
            return drawLineChart(opts, ctx);
        case CHART_TYPES.COLUMN:
            return drawColumnChart(opts, ctx);
        case CHART_TYPES.PIE:
            return drawPieChart(opts, ctx);
        case CHART_TYPES.RING:
            return drawRingChart(opts, ctx);
        case CHART_TYPES.AREA:
            return drawAreaChart(opts, ctx);
        case CHART_TYPES.RADAR:
            return drawRadarChart(opts, ctx);
        default:
            throw new Error('Unsupported chart type: ' + chartType);
    }
}

// 绘制柱状图
function drawColumnChart(opts, ctx) {
    console.log('Drawing column chart with options:', opts);

    var categories = opts.categories || [];
    var series = opts.series || [];
    var width = opts.width || 300;
    var height = opts.height || 200;
    var yAxisWidth = opts.yAxisWidth || config.yAxisWidth;
    var xAxisHeight = opts.xAxisHeight || config.xAxisHeight;
    var padding = opts.padding || config.padding;
    var columePadding = opts.columePadding || config.columePadding;
    var colors = opts.colors || config.colors;
    var fontSize = opts.fontSize || config.fontSize;

    console.log('Categories:', categories);
    console.log('Series:', series);

    // 检查数据是否有效
    if (!categories || categories.length === 0) {
        console.error('Categories are required for column chart');
        return {
            type: 'column',
            ctx: ctx,
            config: opts
        };
    }

    if (!series || series.length === 0) {
        console.error('Series data is required for column chart');
        return {
            type: 'column',
            ctx: ctx,
            config: opts
        };
    }

    // 计算图表区域
    var chartArea = {
        width: width - 2 * padding - yAxisWidth,
        height: height - 2 * padding - xAxisHeight,
        x: padding + yAxisWidth,
        y: padding
    };

    console.log('Chart area:', chartArea);

    // 计算最大值和最小值
    var maxValue = 0;
    var minValue = 0;

    series.forEach(function(item) {
        var data = item.data;
        if (!data || !Array.isArray(data)) {
            console.error('Invalid series data:', item);
            return;
        }

        data.forEach(function(value) {
            if (value > maxValue) {
                maxValue = value;
            }
            if (value < minValue) {
                minValue = value;
            }
        });
    });

    console.log('Max value:', maxValue, 'Min value:', minValue);

    // 向上取整，使图表更美观
    if (maxValue > 0) {
        maxValue = Math.ceil(maxValue / 10) * 10;
    }
    if (minValue < 0) {
        minValue = Math.floor(minValue / 10) * 10;
    }

    console.log('Adjusted max value:', maxValue, 'Adjusted min value:', minValue);

    // 绘制背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制Y轴
    var yAxisSplit = opts.yAxisSplit || config.yAxisSplit;
    var step = (maxValue - minValue) / yAxisSplit;

    for (var i = 0; i <= yAxisSplit; i++) {
        var value = maxValue - i * step;
        var y = chartArea.y + (i / yAxisSplit) * chartArea.height;

        // 绘制网格线
        ctx.beginPath();
        ctx.setStrokeStyle('#e0e0e0');
        ctx.setLineWidth(1);
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();

        // 绘制Y轴刻度
        ctx.setFillStyle('#666666');
        ctx.setTextAlign('right');
        ctx.setFontSize(fontSize);
        ctx.fillText(formatNumber(value), chartArea.x - 5, y + 3);
    }

    // 绘制X轴
    var columnWidth = (chartArea.width - 2 * padding) / categories.length;

    categories.forEach(function(category, index) {
        var x = chartArea.x + index * columnWidth + columnWidth / 2;
        var y = chartArea.y + chartArea.height + 15;

        // 绘制X轴刻度
        ctx.setFillStyle('#666666');
        ctx.setTextAlign('center');
        ctx.setFontSize(fontSize);
        ctx.fillText(category, x, y);
    });

    // 绘制柱状图
    series.forEach(function(serie, serieIndex) {
        var data = serie.data;
        var color = serie.color || colors[serieIndex % colors.length];

        data.forEach(function(value, index) {
            var height = (Math.abs(value) / (maxValue - minValue)) * chartArea.height;
            var x = chartArea.x + index * columnWidth + columePadding;
            var y = value >= 0 ? chartArea.y + chartArea.height - height : chartArea.y;

            // 绘制柱子
            ctx.setFillStyle(color);
            ctx.fillRect(x, y, columnWidth - 2 * columePadding, height);

            // 绘制数值
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('center');
            ctx.setFontSize(fontSize);
            ctx.fillText(formatNumber(value), x + (columnWidth - 2 * columePadding) / 2, y - 5);
        });
    });

    // 绘制图例
    if (opts.showLegend !== false) {
        var legendHeight = config.legendHeight;
        var legendWidth = 0;
        var legendMargin = 5;

        series.forEach(function(serie) {
            legendWidth += ctx.measureText(serie.name).width + 15 + legendMargin;
        });

        var legendX = (width - legendWidth) / 2;
        var legendY = height - legendHeight;

        series.forEach(function(serie, index) {
            var color = serie.color || colors[index % colors.length];

            // 绘制图例颜色块
            ctx.setFillStyle(color);
            ctx.fillRect(legendX, legendY, 10, 10);

            // 绘制图例文字
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('left');
            ctx.setFontSize(fontSize);
            ctx.fillText(serie.name, legendX + 15, legendY + 8);

            legendX += ctx.measureText(serie.name).width + 15 + legendMargin;
        });
    }

    // 执行绘制
    ctx.draw();

    return {
        type: 'column',
        ctx: ctx,
        config: opts
    };
}

// 绘制折线图
function drawLineChart(opts, ctx) {
    var categories = opts.categories || [];
    var series = opts.series || [];
    var width = opts.width || 300;
    var height = opts.height || 200;
    var yAxisWidth = opts.yAxisWidth || config.yAxisWidth;
    var xAxisHeight = opts.xAxisHeight || config.xAxisHeight;
    var padding = opts.padding || config.padding;
    var colors = opts.colors || config.colors;
    var fontSize = opts.fontSize || config.fontSize;

    // 计算图表区域
    var chartArea = {
        width: width - 2 * padding - yAxisWidth,
        height: height - 2 * padding - xAxisHeight,
        x: padding + yAxisWidth,
        y: padding
    };

    // 计算最大值和最小值
    var maxValue = 0;
    var minValue = 0;

    series.forEach(function(item) {
        var data = item.data;
        data.forEach(function(value) {
            if (value > maxValue) {
                maxValue = value;
            }
            if (value < minValue) {
                minValue = value;
            }
        });
    });

    // 向上取整，使图表更美观
    if (maxValue > 0) {
        maxValue = Math.ceil(maxValue / 10) * 10;
    }
    if (minValue < 0) {
        minValue = Math.floor(minValue / 10) * 10;
    }

    // 绘制背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制Y轴
    var yAxisSplit = opts.yAxisSplit || config.yAxisSplit;
    var step = (maxValue - minValue) / yAxisSplit;

    for (var i = 0; i <= yAxisSplit; i++) {
        var value = maxValue - i * step;
        var y = chartArea.y + (i / yAxisSplit) * chartArea.height;

        // 绘制网格线
        ctx.beginPath();
        ctx.setStrokeStyle('#e0e0e0');
        ctx.setLineWidth(1);
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();

        // 绘制Y轴刻度
        ctx.setFillStyle('#666666');
        ctx.setTextAlign('right');
        ctx.setFontSize(fontSize);
        ctx.fillText(formatNumber(value), chartArea.x - 5, y + 3);
    }

    // 绘制X轴
    var xStep = chartArea.width / (categories.length - 1);

    categories.forEach(function(category, index) {
        var x = chartArea.x + index * xStep;
        var y = chartArea.y + chartArea.height + 15;

        // 绘制X轴刻度
        ctx.setFillStyle('#666666');
        ctx.setTextAlign('center');
        ctx.setFontSize(fontSize);
        ctx.fillText(category, x, y);
    });

    // 绘制折线图
    series.forEach(function(serie, serieIndex) {
        var data = serie.data;
        var color = serie.color || colors[serieIndex % colors.length];
        var points = [];

        data.forEach(function(value, index) {
            var x = chartArea.x + index * xStep;
            var y = chartArea.y + (1 - (value - minValue) / (maxValue - minValue)) * chartArea.height;

            points.push([x, y]);

            // 绘制数据点
            ctx.beginPath();
            ctx.setFillStyle(color);
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();

            // 绘制数值
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('center');
            ctx.setFontSize(fontSize);
            ctx.fillText(formatNumber(value), x, y - 10);
        });

        // 绘制折线
        ctx.beginPath();
        ctx.setStrokeStyle(color);
        ctx.setLineWidth(2);

        points.forEach(function(point, index) {
            if (index === 0) {
                ctx.moveTo(point[0], point[1]);
            } else {
                ctx.lineTo(point[0], point[1]);
            }
        });

        ctx.stroke();

        // 绘制面积
        if (opts.fillArea) {
            ctx.beginPath();
            ctx.setFillStyle(color + '33'); // 添加透明度

            points.forEach(function(point, index) {
                if (index === 0) {
                    ctx.moveTo(point[0], point[1]);
                } else {
                    ctx.lineTo(point[0], point[1]);
                }
            });

            ctx.lineTo(points[points.length - 1][0], chartArea.y + chartArea.height);
            ctx.lineTo(points[0][0], chartArea.y + chartArea.height);
            ctx.closePath();
            ctx.fill();
        }
    });

    // 绘制图例
    if (opts.showLegend !== false) {
        var legendHeight = config.legendHeight;
        var legendWidth = 0;
        var legendMargin = 5;

        series.forEach(function(serie) {
            legendWidth += ctx.measureText(serie.name).width + 15 + legendMargin;
        });

        var legendX = (width - legendWidth) / 2;
        var legendY = height - legendHeight;

        series.forEach(function(serie, index) {
            var color = serie.color || colors[index % colors.length];

            // 绘制图例颜色块
            ctx.setFillStyle(color);
            ctx.fillRect(legendX, legendY, 10, 10);

            // 绘制图例文字
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('left');
            ctx.setFontSize(fontSize);
            ctx.fillText(serie.name, legendX + 15, legendY + 8);

            legendX += ctx.measureText(serie.name).width + 15 + legendMargin;
        });
    }

    // 执行绘制
    ctx.draw();

    return {
        type: 'line',
        ctx: ctx,
        config: opts
    };
}

// 绘制饼图
function drawPieChart(opts, ctx) {
    var series = opts.series || [];
    var width = opts.width || 300;
    var height = opts.height || 200;
    var padding = opts.padding || config.padding;
    var colors = opts.colors || config.colors;
    var fontSize = opts.fontSize || config.fontSize;

    // 计算图表区域
    var chartArea = {
        width: width - 2 * padding,
        height: height - 2 * padding,
        x: padding,
        y: padding
    };

    // 计算饼图半径
    var radius = Math.min(chartArea.width, chartArea.height) / 2;

    // 计算饼图中心点
    var centerX = chartArea.x + chartArea.width / 2;
    var centerY = chartArea.y + chartArea.height / 2;

    // 计算总和
    var total = 0;
    series.forEach(function(item) {
        total += item.data;
    });

    // 绘制背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制饼图
    var startAngle = 0;

    series.forEach(function(item, index) {
        var color = item.color || colors[index % colors.length];
        var percent = item.data / total;
        var endAngle = startAngle + percent * 2 * Math.PI;

        // 绘制扇形
        ctx.beginPath();
        ctx.setFillStyle(color);
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.closePath();
        ctx.fill();

        // 绘制标签
        var labelAngle = startAngle + (endAngle - startAngle) / 2;
        var labelX = centerX + Math.cos(labelAngle) * (radius + 15);
        var labelY = centerY + Math.sin(labelAngle) * (radius + 15);

        ctx.setFillStyle('#333333');
        ctx.setTextAlign(labelX > centerX ? 'left' : 'right');
        ctx.setFontSize(fontSize);
        ctx.fillText(item.name + ': ' + formatNumber(percent * 100) + '%', labelX, labelY);

        startAngle = endAngle;
    });

    // 执行绘制
    ctx.draw();

    return {
        type: 'pie',
        ctx: ctx,
        config: opts
    };
}

// 绘制环形图
function drawRingChart(opts, ctx) {
    var series = opts.series || [];
    var width = opts.width || 300;
    var height = opts.height || 200;
    var padding = opts.padding || config.padding;
    var colors = opts.colors || config.colors;
    var fontSize = opts.fontSize || config.fontSize;
    var innerRadius = opts.innerRadius || 0.6; // 内圆半径比例

    // 计算图表区域
    var chartArea = {
        width: width - 2 * padding,
        height: height - 2 * padding,
        x: padding,
        y: padding
    };

    // 计算环形图半径
    var radius = Math.min(chartArea.width, chartArea.height) / 2;
    var innerRadiusValue = radius * innerRadius;

    // 计算环形图中心点
    var centerX = chartArea.x + chartArea.width / 2;
    var centerY = chartArea.y + chartArea.height / 2;

    // 计算总和
    var total = 0;
    series.forEach(function(item) {
        total += item.data;
    });

    // 绘制背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制环形图
    var startAngle = 0;

    series.forEach(function(item, index) {
        var color = item.color || colors[index % colors.length];
        var percent = item.data / total;
        var endAngle = startAngle + percent * 2 * Math.PI;

        // 绘制扇形
        ctx.beginPath();
        ctx.setFillStyle(color);
        ctx.moveTo(centerX + innerRadiusValue * Math.cos(startAngle), centerY + innerRadiusValue * Math.sin(startAngle));
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.arc(centerX, centerY, innerRadiusValue, endAngle, startAngle, true);
        ctx.closePath();
        ctx.fill();

        // 绘制标签
        var labelAngle = startAngle + (endAngle - startAngle) / 2;
        var labelX = centerX + Math.cos(labelAngle) * (radius + 15);
        var labelY = centerY + Math.sin(labelAngle) * (radius + 15);

        ctx.setFillStyle('#333333');
        ctx.setTextAlign(labelX > centerX ? 'left' : 'right');
        ctx.setFontSize(fontSize);
        ctx.fillText(item.name + ': ' + formatNumber(percent * 100) + '%', labelX, labelY);

        startAngle = endAngle;
    });

    // 绘制中心文字
    if (opts.title) {
        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.setFontSize(fontSize * 1.5);
        ctx.fillText(opts.title, centerX, centerY);
    }

    // 执行绘制
    ctx.draw();

    return {
        type: 'ring',
        ctx: ctx,
        config: opts
    };
}

// 绘制面积图
function drawAreaChart(opts, ctx) {
    // 面积图实际上是填充区域的折线图
    opts.fillArea = true;
    return drawLineChart(opts, ctx);
}

// 绘制雷达图
function drawRadarChart(opts, ctx) {
    var series = opts.series || [];
    var categories = opts.categories || [];
    var width = opts.width || 300;
    var height = opts.height || 200;
    var padding = opts.padding || config.padding;
    var colors = opts.colors || config.colors;
    var fontSize = opts.fontSize || config.fontSize;

    // 计算图表区域
    var chartArea = {
        width: width - 2 * padding,
        height: height - 2 * padding,
        x: padding,
        y: padding
    };

    // 计算雷达图半径
    var radius = Math.min(chartArea.width, chartArea.height) / 2;

    // 计算雷达图中心点
    var centerX = chartArea.x + chartArea.width / 2;
    var centerY = chartArea.y + chartArea.height / 2;

    // 计算最大值
    var maxValue = 0;

    series.forEach(function(item) {
        var data = item.data;
        data.forEach(function(value) {
            if (value > maxValue) {
                maxValue = value;
            }
        });
    });

    // 向上取整，使图表更美观
    maxValue = Math.ceil(maxValue / 10) * 10;

    // 绘制背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制雷达图网格
    var gridCount = opts.gridCount || config.radarGridCount;
    var angleStep = (2 * Math.PI) / categories.length;

    for (var i = 1; i <= gridCount; i++) {
        var r = radius * (i / gridCount);

        ctx.beginPath();
        ctx.setStrokeStyle('#e0e0e0');
        ctx.setLineWidth(1);

        for (var j = 0; j < categories.length; j++) {
            var angle = j * angleStep;
            var x = centerX + r * Math.cos(angle - Math.PI / 2);
            var y = centerY + r * Math.sin(angle - Math.PI / 2);

            if (j === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.closePath();
        ctx.stroke();
    }

    // 绘制雷达图轴线
    for (var i = 0; i < categories.length; i++) {
        var angle = i * angleStep;
        var x = centerX + radius * Math.cos(angle - Math.PI / 2);
        var y = centerY + radius * Math.sin(angle - Math.PI / 2);

        ctx.beginPath();
        ctx.setStrokeStyle('#e0e0e0');
        ctx.setLineWidth(1);
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(x, y);
        ctx.stroke();

        // 绘制类别标签
        var labelX = centerX + (radius + 15) * Math.cos(angle - Math.PI / 2);
        var labelY = centerY + (radius + 15) * Math.sin(angle - Math.PI / 2);

        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.setFontSize(fontSize);
        ctx.fillText(categories[i], labelX, labelY);
    }

    // 绘制雷达图数据
    series.forEach(function(serie, serieIndex) {
        var data = serie.data;
        var color = serie.color || colors[serieIndex % colors.length];
        var points = [];

        // 绘制数据区域
        ctx.beginPath();
        ctx.setFillStyle(color + '33'); // 添加透明度
        ctx.setStrokeStyle(color);
        ctx.setLineWidth(2);

        for (var i = 0; i < categories.length; i++) {
            var angle = i * angleStep;
            var value = data[i];
            var r = radius * (value / maxValue);
            var x = centerX + r * Math.cos(angle - Math.PI / 2);
            var y = centerY + r * Math.sin(angle - Math.PI / 2);

            points.push([x, y]);

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.closePath();
        ctx.fill();
        ctx.stroke();

        // 绘制数据点
        points.forEach(function(point) {
            ctx.beginPath();
            ctx.setFillStyle(color);
            ctx.arc(point[0], point[1], 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    });

    // 绘制图例
    if (opts.showLegend !== false) {
        var legendHeight = config.legendHeight;
        var legendWidth = 0;
        var legendMargin = 5;

        series.forEach(function(serie) {
            legendWidth += ctx.measureText(serie.name).width + 15 + legendMargin;
        });

        var legendX = (width - legendWidth) / 2;
        var legendY = height - legendHeight;

        series.forEach(function(serie, index) {
            var color = serie.color || colors[index % colors.length];

            // 绘制图例颜色块
            ctx.setFillStyle(color);
            ctx.fillRect(legendX, legendY, 10, 10);

            // 绘制图例文字
            ctx.setFillStyle('#333333');
            ctx.setTextAlign('left');
            ctx.setFontSize(fontSize);
            ctx.fillText(serie.name, legendX + 15, legendY + 8);

            legendX += ctx.measureText(serie.name).width + 15 + legendMargin;
        });
    }

    // 执行绘制
    ctx.draw();

    return {
        type: 'radar',
        ctx: ctx,
        config: opts
    };
}

// 导出模块
module.exports = {
    create: create,
    CHART_TYPES: CHART_TYPES
};
