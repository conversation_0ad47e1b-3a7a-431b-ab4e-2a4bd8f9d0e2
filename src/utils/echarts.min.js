/*
 * ECharts 小程序版本
 * 请从 https://github.com/ecomfe/echarts-for-weixin 下载完整版本
 * 这里只是一个占位文件
 */

// 简化版的 ECharts 对象
export function init(canvas, theme, opts) {
  // 创建一个简单的图表对象
  const chart = {
    setOption: function(option) {
      console.log('设置图表选项:', option);
      // 在实际的 ECharts 中，这里会根据选项渲染图表
    },
    getZr: function() {
      return {
        handler: {
          dispatch: function() {},
          processGesture: function() {}
        }
      };
    },
    clear: function() {
      console.log('清除图表');
    },
    dispose: function() {
      console.log('销毁图表');
    }
  };
  
  return chart;
}

// 设置 Canvas 创建器
export function setCanvasCreator(creator) {
  console.log('设置 Canvas 创建器');
}
