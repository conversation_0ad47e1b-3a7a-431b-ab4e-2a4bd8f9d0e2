/**
 * HTTP请求工具类
 */
import config from '@/config/index';

// 清除用户数据的辅助函数
const clearUserData = () => {
  // 清除token、登录状态和用户信息
  uni.removeStorageSync('token');
  uni.removeStorageSync('tokenExpireAt');
  uni.removeStorageSync('isLoggedIn');
  uni.removeStorageSync('userInfo');
  uni.removeStorageSync('userAdmin');
  uni.removeStorageSync('userGroupAdmin');
  uni.removeStorageSync('userGroupId');
  uni.removeStorageSync('userGroupName');
  uni.removeStorageSync('userId');
  uni.removeStorageSync('userName');
  uni.removeStorageSync('userRealName');

  // 清除角色相关信息
  uni.removeStorageSync('userRoleIdList');
  uni.removeStorageSync('userRoleTypes');
  uni.removeStorageSync('isAdmin');
  uni.removeStorageSync('isOperator');
  uni.removeStorageSync('isGroupAdmin');
  uni.removeStorageSync('isGroupOperator');
  uni.removeStorageSync('isInspector');
  uni.removeStorageSync('isVisitor');
};

// 请求拦截器
const requestInterceptor = (options) => {
  // 添加基础URL
  if (options.url && !options.url.startsWith('http')) {
    options.url = `${config.apiUrl}${options.url}`;
  }

  // 添加通用header
  options.header = {
    'Content-Type': 'application/json',
    ...options.header
  };

  // 检查token是否存在和是否过期
  const token = uni.getStorageSync('token');
  const tokenExpireAt = uni.getStorageSync('tokenExpireAt');

  if (token) {
    // 检查token是否过期
    const now = Date.now();
    if (tokenExpireAt && now >= tokenExpireAt) {
      // token已过期，清除存储的信息并跳转到登录页
      clearUserData();
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);

      // 返回一个会被拒绝的请求，阻止后续请求发送
      options.fail = true;
      return options;
    }

    // token未过期，添加到请求头
    options.header.xaspsession = token;
  }

  // 调试日志
  if (config.debug) {
    console.log('Request:', options);
  }

  return options;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 调试日志
  if (config.debug) {
    console.log('Response:', response);
  }

  // 处理HTTP状态码
  if (response.statusCode >= 200 && response.statusCode < 300) {
    return response.data;
  } else if (response.statusCode === 401) {
    // 未授权，清除用户数据并跳转到登录页
    clearUserData();

    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    }, 1500);
    return Promise.reject(response);
  } else {
    // 其他错误
    uni.showToast({
      title: response.data?.message || '请求失败',
      icon: 'none'
    });
    return Promise.reject(response);
  }
};

/**
 * 发送HTTP请求
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  // 应用请求拦截器
  options = requestInterceptor(options);

  // 如果请求被拒绝（例如token过期），直接返回拒绝的Promise
  if (options.fail) {
    return Promise.reject({ message: '请求被拒绝，可能是因为登录已过期' });
  }

  // 发送请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      timeout: options.timeout || config.timeout,
      success: (res) => {
        try {
          const result = responseInterceptor(res, options);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        if (config.debug) {
          console.error('Request failed:', err);
        }
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求参数
 * @param {Object} options - 其他选项
 * @returns {Promise} - 返回Promise对象
 */
const get = (url, data = {}, options = {}) => {
  return request({
    url,
    data,
    method: 'GET',
    ...options
  });
};

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 返回Promise对象
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    data,
    method: 'POST',
    ...options
  });
};

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 返回Promise对象
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    data,
    method: 'PUT',
    ...options
  });
};

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求参数
 * @param {Object} options - 其他选项
 * @returns {Promise} - 返回Promise对象
 */
const del = (url, data = {}, options = {}) => {
  return request({
    url,
    data,
    method: 'DELETE',
    ...options
  });
};

// 导出请求方法
export default {
  request,
  get,
  post,
  put,
  delete: del
};
