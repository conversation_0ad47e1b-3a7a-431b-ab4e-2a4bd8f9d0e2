/**
 * 角色类型枚举
 */
export const RoleType = {
  ADMIN: 'ADMIN', // 平台管理员
  OPERATOR: 'OPERATOR', // 平台操作员
  GROUP_ADMIN: 'GROUP_ADMIN', // 租户管理员
  GROUP_OPERATOR: 'GROUP_OPERATOR', // 租户操作员
  INSPECTOR: 'INSPECTOR', // 租户点检员
  VISITOR: 'VISITOR' // 租户游客
};

/**
 * 角色ID映射
 */
export const ROLE_ID_MAP = {
  '10629223438942208': RoleType.ADMIN, // 平台管理员
  '10629224487256064': RoleType.OPERATOR, // 平台操作员
  '10629246817468416': RoleType.GROUP_ADMIN, // 租户管理员
  '10629247561695232': RoleType.GROUP_OPERATOR, // 租户操作员
  '10629222488670208': RoleType.INSPECTOR, // 租户点检员
  '10629221809192960': RoleType.VISITOR // 租户游客
};

/**
 * 根据角色ID获取角色类型
 * @param {string} roleId - 角色ID
 * @returns {string} - 角色类型
 */
export function getRoleTypeById(roleId) {
  return ROLE_ID_MAP[roleId] || null;
}

/**
 * 检查用户是否具有指定角色
 * @param {Array} roleIdList - 用户的角色ID列表
 * @param {string} roleType - 要检查的角色类型
 * @returns {boolean} - 是否具有指定角色
 */
export function hasRole(roleIdList, roleType) {
  if (!roleIdList || !Array.isArray(roleIdList) || roleIdList.length === 0) {
    return false;
  }
  
  return roleIdList.some(roleId => ROLE_ID_MAP[roleId] === roleType);
}

/**
 * 获取用户的所有角色类型
 * @param {Array} roleIdList - 用户的角色ID列表
 * @returns {Array} - 角色类型列表
 */
export function getUserRoleTypes(roleIdList) {
  if (!roleIdList || !Array.isArray(roleIdList) || roleIdList.length === 0) {
    return [];
  }
  
  return roleIdList
    .map(roleId => ROLE_ID_MAP[roleId])
    .filter(roleType => roleType !== undefined);
}

/**
 * 获取角色类型的中文名称
 * @param {string} roleType - 角色类型
 * @returns {string} - 角色类型的中文名称
 */
export function getRoleTypeName(roleType) {
  const roleTypeNames = {
    [RoleType.ADMIN]: '平台管理员',
    [RoleType.OPERATOR]: '平台操作员',
    [RoleType.GROUP_ADMIN]: '租户管理员',
    [RoleType.GROUP_OPERATOR]: '租户操作员',
    [RoleType.INSPECTOR]: '租户点检员',
    [RoleType.VISITOR]: '租户游客'
  };
  
  return roleTypeNames[roleType] || '未知角色';
}
