<template>
	<view class="container">
		<view class="header">
			<text class="title">蓝牙通信</text>
			<text class="device-name">{{ deviceName }}</text>
		</view>

		<view class="connection-status">
			<view class="status-indicator" :class="{ 'connected': isConnected }"></view>
			<text class="status-text">{{ isConnected ? '已连接' : '未连接' }}</text>
		</view>

		<!-- 数据接收区域 -->
		<view class="data-section">
			<view class="section-header">
				<text class="section-title">接收数据</text>
				<button class="btn-clear" @click="clearReceiveData">清空</button>
			</view>
			<scroll-view class="data-display" scroll-y="true" :scroll-top="scrollTop">
				<view v-if="receiveDataList.length === 0" class="empty-tip">
					暂无接收数据
				</view>
				<view 
					v-for="(item, index) in receiveDataList" 
					:key="index"
					class="data-item receive-item"
				>
					<view class="data-time">{{ item.time }}</view>
					<view class="data-content">{{ item.data }}</view>
				</view>
			</scroll-view>
		</view>

		<!-- 数据发送区域 -->
		<view class="send-section" v-if="canWrite">
			<view class="section-header">
				<text class="section-title">发送数据</text>
			</view>
			<view class="send-input-area">
				<input 
					class="send-input" 
					v-model="sendData" 
					placeholder="请输入要发送的数据"
					@confirm="sendMessage"
				/>
				<button class="btn-send" @click="sendMessage" :disabled="!sendData.trim()">
					发送
				</button>
			</view>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-section">
			<button 
				v-if="canNotify" 
				class="btn-action" 
				:class="{ 'active': isNotifying }"
				@click="toggleNotify"
			>
				{{ isNotifying ? '停止监听' : '开始监听' }}
			</button>
			<button 
				v-if="canRead" 
				class="btn-action" 
				@click="readData"
			>
				读取数据
			</button>
			<button class="btn-action btn-disconnect" @click="disconnect">
				断开连接
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			deviceId: '',
			deviceName: '',
			serviceId: '',
			characteristicId: '',
			canRead: false,
			canWrite: false,
			canNotify: false,
			isConnected: true,
			isNotifying: false,
			sendData: '',
			receiveDataList: [],
			scrollTop: 0
		}
	},
	
	onLoad(options) {
		this.deviceId = options.deviceId || '';
		this.deviceName = decodeURIComponent(options.deviceName || '未知设备');
		this.serviceId = options.serviceId || '';
		this.characteristicId = options.characteristicId || '';
		this.canRead = options.canRead === 'true';
		this.canWrite = options.canWrite === 'true';
		this.canNotify = options.canNotify === 'true';
		
		console.log('通信页面参数:', {
			deviceId: this.deviceId,
			serviceId: this.serviceId,
			characteristicId: this.characteristicId,
			canRead: this.canRead,
			canWrite: this.canWrite,
			canNotify: this.canNotify
		});
		
		// 如果支持通知，自动开始监听
		if (this.canNotify) {
			this.startNotify();
		}
	},
	
	onUnload() {
		// 停止监听并断开连接
		this.stopNotify();
		this.disconnect();
	},
	
	methods: {
		// 开始监听通知
		startNotify() {
			// #ifdef MP-WEIXIN
			wx.notifyBLECharacteristicValueChange({
				deviceId: this.deviceId,
				serviceId: this.serviceId,
				characteristicId: this.characteristicId,
				state: true,
				success: () => {
					console.log('开始监听特征值变化');
					this.isNotifying = true;
					
					// 监听特征值变化
					wx.onBLECharacteristicValueChange((res) => {
						this.handleReceiveData(res.value);
					});
				},
				fail: (err) => {
					console.error('开始监听失败:', err);
					uni.showToast({
						title: '开始监听失败',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 停止监听通知
		stopNotify() {
			// #ifdef MP-WEIXIN
			if (this.isNotifying) {
				wx.notifyBLECharacteristicValueChange({
					deviceId: this.deviceId,
					serviceId: this.serviceId,
					characteristicId: this.characteristicId,
					state: false,
					success: () => {
						console.log('停止监听特征值变化');
						this.isNotifying = false;
						wx.offBLECharacteristicValueChange();
					},
					fail: (err) => {
						console.error('停止监听失败:', err);
					}
				});
			}
			// #endif
		},
		
		// 切换监听状态
		toggleNotify() {
			if (this.isNotifying) {
				this.stopNotify();
			} else {
				this.startNotify();
			}
		},
		
		// 读取数据
		readData() {
			// #ifdef MP-WEIXIN
			wx.readBLECharacteristicValue({
				deviceId: this.deviceId,
				serviceId: this.serviceId,
				characteristicId: this.characteristicId,
				success: () => {
					console.log('读取数据成功');
					uni.showToast({
						title: '读取成功',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('读取数据失败:', err);
					uni.showToast({
						title: '读取失败',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 发送数据
		sendMessage() {
			if (!this.sendData.trim()) {
				return;
			}
			
			// 将字符串转换为ArrayBuffer
			const buffer = this.stringToArrayBuffer(this.sendData);
			
			// #ifdef MP-WEIXIN
			wx.writeBLECharacteristicValue({
				deviceId: this.deviceId,
				serviceId: this.serviceId,
				characteristicId: this.characteristicId,
				value: buffer,
				success: () => {
					console.log('发送数据成功:', this.sendData);
					this.addReceiveData(`发送: ${this.sendData}`, 'send');
					this.sendData = '';
					uni.showToast({
						title: '发送成功',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('发送数据失败:', err);
					uni.showToast({
						title: '发送失败',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 处理接收到的数据
		handleReceiveData(buffer) {
			const data = this.arrayBufferToString(buffer);
			console.log('接收到数据:', data);
			this.addReceiveData(`接收: ${data}`, 'receive');
		},
		
		// 添加接收数据到列表
		addReceiveData(data, type = 'receive') {
			const now = new Date();
			const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
			
			this.receiveDataList.push({
				time,
				data,
				type
			});
			
			// 自动滚动到底部
			this.$nextTick(() => {
				this.scrollTop = this.receiveDataList.length * 100;
			});
		},
		
		// 清空接收数据
		clearReceiveData() {
			this.receiveDataList = [];
		},
		
		// 断开连接
		disconnect() {
			this.stopNotify();
			
			// #ifdef MP-WEIXIN
			wx.closeBLEConnection({
				deviceId: this.deviceId,
				success: () => {
					console.log('蓝牙连接已断开');
					this.isConnected = false;
					uni.showToast({
						title: '连接已断开',
						icon: 'success'
					});
					
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				},
				fail: (err) => {
					console.error('断开连接失败:', err);
					uni.navigateBack();
				}
			});
			// #endif
		},
		
		// 字符串转ArrayBuffer
		stringToArrayBuffer(str) {
			const encoder = new TextEncoder();
			return encoder.encode(str).buffer;
		},
		
		// ArrayBuffer转字符串
		arrayBufferToString(buffer) {
			const decoder = new TextDecoder();
			return decoder.decode(buffer);
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.device-name {
	font-size: 28rpx;
	color: #666;
}

.connection-status {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background-color: #fff;
	border-radius: 12rpx;
}

.status-indicator {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background-color: #f44336;
	margin-right: 15rpx;
}

.status-indicator.connected {
	background-color: #4caf50;
}

.status-text {
	font-size: 28rpx;
	color: #333;
}

.data-section {
	margin-bottom: 30rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.btn-clear {
	background-color: #ff9800;
	color: #fff;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 8rpx;
}

.data-display {
	height: 400rpx;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
}

.empty-tip {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 60rpx 0;
}

.data-item {
	margin-bottom: 20rpx;
	padding: 15rpx;
	border-radius: 8rpx;
	background-color: #f8f9fa;
}

.receive-item {
	border-left: 6rpx solid #4caf50;
}

.data-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.data-content {
	font-size: 28rpx;
	color: #333;
	word-break: break-all;
}

.send-section {
	margin-bottom: 30rpx;
}

.send-input-area {
	display: flex;
	gap: 15rpx;
	background-color: #fff;
	padding: 20rpx;
	border-radius: 12rpx;
}

.send-input {
	flex: 1;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.btn-send {
	background-color: #1976D2;
	color: #fff;
	padding: 20rpx 30rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.btn-send:disabled {
	background-color: #ccc;
}

.action-section {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.btn-action {
	background-color: #1976D2;
	color: #fff;
	padding: 25rpx;
	border-radius: 12rpx;
	font-size: 30rpx;
	text-align: center;
}

.btn-action.active {
	background-color: #4caf50;
}

.btn-disconnect {
	background-color: #f44336;
}
</style>
