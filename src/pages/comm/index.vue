<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="nav-wrap">
			<text class="nav-title">{{ deviceName }}</text>
		</view>

		<!-- 接收框 -->
		<view class="rec-textarea-wrap">
			<textarea
				class="rec-textarea-content"
				placeholder="接收框"
				:disabled="true"
				:value="recData"
			></textarea>
		</view>

		<view class="operations-wrap">
			<view class="operations-total">
				<view class="tag-wrap">RX：{{ recTotal }}</view>
				<view class="tag-wrap">TX：{{ sendTotal }}</view>
			</view>
			<view class="operations-btns">
				<view
					v-for="(item, index) in checkboxOptions"
					:key="index"
					class="operations-btns_checkBox"
					@click="onHexChange(index)"
				>
					<text>{{ item.label }}</text>
					<view class="checkbox" :class="{ 'checked': item.value }"></view>
				</view>
				<button class="btn-small" @click="cleanRecTextarea">清空</button>
			</view>
		</view>

		<!-- 发送框 -->
		<view class="send-textarea-wrap">
			<textarea
				class="send-textarea-content"
				placeholder="发送框"
				:value="sendData"
				@input="onSendTextareaChange"
				:disabled="isAutoSend"
			></textarea>
		</view>

		<view class="operations-wrap">
			<view class="operations-timer">
				<view class="operations-btns_checkBox" @click="onAutoSendToggle">
					<text>循环发送</text>
					<view class="checkbox" :class="{ 'checked': isAutoSend }"></view>
				</view>
				<input
					class="input-class"
					type="number"
					:value="interval"
					:disabled="isAutoSend"
					@input="onIntervalChange"
				/>
				<text style="font-size: 30rpx">ms</text>
			</view>
			<view class="operations-btns">
				<button class="btn-small" style="margin-right: 15rpx" @click="cleanSendTextarea">清空</button>
				<button class="btn-primary btn-small" @click="onSendBtnTap">发 送</button>
			</view>
		</view>
	</view>
</template>

<script>
// 按照原来的逻辑引入依赖
// import { abToHex, hexToAb, splitPackage, check02Model } from '@utils/index'
// import { TextDecoder, TextEncoder } from '@libs/encoding'
// import { HC02CharacteristicNotifyUUID } from '@configs/index'

// 暂时使用简化版本的工具函数
function abToHex(buffer) {
	const hexArr = Array.prototype.map.call(
		new Uint8Array(buffer),
		function(bit) {
			return ('00' + bit.toString(16)).slice(-2)
		}
	)
	return hexArr.join('')
}

function hexToAb(hex) {
	const str = hex.replace(/\s/g, '')
	const buffer = new ArrayBuffer(str.length / 2)
	const view = new Uint8Array(buffer)
	for (let i = 0; i < str.length; i += 2) {
		view[i / 2] = parseInt(str.substr(i, 2), 16)
	}
	return buffer
}

function splitPackage(ab, size = 20) {
	const packages = []
	const view = new Uint8Array(ab)
	for (let i = 0; i < view.length; i += size) {
		packages.push(view.slice(i, i + size).buffer)
	}
	return packages
}

// 简化版本的编码器
const gbkDecoder = {
	decode: (buffer) => {
		try {
			return new TextDecoder('utf-8').decode(buffer)
		} catch (e) {
			return String.fromCharCode.apply(null, new Uint8Array(buffer))
		}
	}
}

const gbkEncoder = {
	encode: (str) => {
		return new TextEncoder().encode(str).buffer
	}
}

export default {
	data() {
		return {
			deviceName: '设备名称',
			recData: '',
			recTotal: 0,
			sendData: '',
			sendTotal: 0,
			isAutoSend: false,
			interval: 1000, // 自动循环发送间隔，单位ms
			is02Model: false, // HC-02的写和读特征值是分开的，需要特殊处理
			checkboxOptions: [
				{ label: '接收Hex', value: false },
				{ label: '发送Hex', value: false }
			],
			// 私有变量
			_timer: null,
			_device: null,
			_recBuffer: null,
			_origin: 'deviceList'
		}
	},

	// 按照原来的逻辑添加 onLoad 方法
	async onLoad(options) {
		console.log('comm 页面 onLoad，参数:', options);
		const { origin, deviceId, deviceName, serviceId, characteristicId, write, read, notify, indicate } = options

		// 按照原来的逻辑，直接使用传入的参数，不检查连接状态
		// 解码设备名称，处理 %20 等 URL 编码
		this.deviceName = decodeURIComponent(deviceName || '未知设备')
		this._origin = origin
		this._device = {
			deviceId,
			deviceName: this.deviceName,
			serviceId,
			characteristicId,
			write: write === 'true' ? true : false,
			read: read === 'true' ? true : false,
			notify: notify === 'true' ? true : false,
			indicate: indicate === 'true' ? true : false
		}

		console.log('设备信息:', this._device);

		// 暂时跳过 HC-02 特殊处理
		// const is02Model = check02Model(serviceId, characteristicId)
		// if (is02Model) {
		//   await wx.notifyBLECharacteristicValueChange({
		//     deviceId,
		//     serviceId,
		//     characteristicId: HC02CharacteristicNotifyUUID,
		//     state: true
		//   })
		//   wx.onBLECharacteristicValueChange(this.readDatas)
		// }

		// 按照原来的逻辑：如果支持通知或指示，直接设置监听
		if (notify === 'true' || indicate === 'true') {
			try {
				console.log('设置特征值变化监听...');
				await wx.notifyBLECharacteristicValueChange({
					deviceId,
					serviceId,
					characteristicId,
					state: true
				});
				console.log('特征值变化监听设置成功');
				// 监听蓝牙设备的特征值变化
				wx.onBLECharacteristicValueChange(this.readDatas);
			} catch (err) {
				console.error('设置特征值监听失败:', err);
				// 按照原来的逻辑，如果失败就失败了，不做特殊处理
			}
		}
	},

	onUnload() {
		if (this._origin === 'deviceList' && wx.getStorageSync('bluetoothAdapterState')) {
			wx.closeBLEConnection({ deviceId: this._device.deviceId })
			wx.removeStorageSync('connectedDeviceId')
		}
		wx.offBLECharacteristicValueChange(this.readDatas)
		clearInterval(this._timer)
		this._recBuffer = null
		this.recData = ''
		this.recTotal = 0
		this.sendData = ''
		this.sendTotal = 0
		this.interval = 1000
	},

	methods: {
		// 向特征值写数据 - 按照原来的逻辑
		async writeDatas(subPackages, index = 0) {
			if (!subPackages) return;
			const { deviceId, serviceId, characteristicId } = this._device;
			const count = subPackages.length;

			while(index < count) {
				try {
					await wx.writeBLECharacteristicValue({
						deviceId,
						serviceId,
						characteristicId,
						value: subPackages[index]
					});
					this.sendTotal = this.sendTotal + subPackages[index].byteLength;
					index++;
				} catch (err) {
					console.error('写入数据包失败:', err);
					// 按照原来的逻辑，失败就跳过这个包，继续下一个
					index++;
				}
			}
		},

		// 从特征值读取数据 - 按照原来的逻辑
		readDatas(res) {
			const { value } = res
			const isHexMode = this.checkboxOptions[0].value
			if (this._recBuffer) {
				let newArray = new Uint8Array(this._recBuffer.byteLength + value.byteLength)
				newArray.set(this._recBuffer)
				newArray.set(new Uint8Array(value), this._recBuffer.byteLength)
				this._recBuffer = newArray
				newArray = null
			} else {
				this._recBuffer = new Uint8Array(value)
			}
			this.recTotal = this.recTotal + value.byteLength
			setTimeout(() => {
				const recData = isHexMode
					? abToHex(this._recBuffer.buffer.slice(this.recTotal > 4000 ? -4000 : 0))
					: gbkDecoder.decode(this._recBuffer).slice(this.recTotal > 4000 ? -4000 : 0)
				this.recData = recData
			}, 200)
		},

		// 更改发送框内容
		onSendTextareaChange(e) {
			this.sendData = e.detail.value
		},

		// 更改循环发送的时间间隔
		onIntervalChange(e) {
			this.interval = Number(e.detail.value)
		},

		// 切换循环发送 - 按照原来的逻辑
		onAutoSendToggle() {
			const value = !this.isAutoSend;
			const { sendData, checkboxOptions, interval } = this;
			this.isAutoSend = value;

			if (value && sendData) {
				this._timer = setInterval(() => {
					const dataPackage = checkboxOptions[1].value
						? hexToAb(sendData)
						: gbkEncoder.encode(sendData);
					const subPackages = splitPackage(dataPackage, 20); // 数据分包，每20个字节一个数据包数组
					if (wx.getStorageSync('connectedDeviceId') && this._device.write) {
						this.writeDatas(subPackages);
					}
				}, interval);
			} else {
				clearInterval(this._timer);
			}
		},

		// Hex模式切换 - 按照原来的逻辑
		onHexChange(index) {
			this.checkboxOptions[index].value = !this.checkboxOptions[index].value
		},

		// 清除接收框内容、收发计数
		cleanRecTextarea() {
			this.recData = ''
			this.recTotal = 0
			this.sendTotal = 0
			this._recBuffer = null
		},

		// 清除发送框内容
		cleanSendTextarea() {
			this.sendData = ''
		},

		// 点击发送 - 按照原来的逻辑
		onSendBtnTap() {
			if (!this._device.write || !wx.getStorageSync('connectedDeviceId')) return;
			const { sendData, checkboxOptions } = this;
			const ab = checkboxOptions[1].value // 判断是否Hex发送
				? hexToAb(sendData)
				: gbkEncoder.encode(sendData);
			const abs = splitPackage(ab, 20); // 数据分包，每20个字符串一个包
			this.writeDatas(abs);
		}
	}
}
</script>

<style>
page {
	padding: 15rpx;
	width: 100vw;
	height: 100vh;
	box-sizing: border-box;
}

.container {
	width: 100%;
	height: 100%;
}

.nav-wrap {
	width: 100vw;
	background-color: #0052d9;
	color: #fff;
	padding: 20rpx;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.nav-title {
	color: #fff;
}

.rec-textarea-wrap {
	display: block;
	padding: 30rpx;
	width: 100%;
	height: 30vh;
	font-size: 32rpx;
	border: 1px solid #ccc;
	border-radius: 8rpx;
	box-sizing: border-box;
	overflow-x: none;
	overflow-y: scroll;
	word-wrap: break-word;
	word-break: normal;
	margin-bottom: 20rpx;
}

.rec-textarea-content {
	width: 100%;
	height: 100%;
	font-size: 32rpx;
	border: none;
	outline: none;
	resize: none;
}

.send-textarea-wrap {
	width: 100%;
	border: 1px solid #ccc;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
}

.send-textarea-content {
	width: 100%;
	height: 30vh;
	font-size: 32rpx;
	padding: 20rpx;
	border: none;
	outline: none;
	resize: none;
	box-sizing: border-box;
}

.operations-wrap {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
}

.operations-total {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.tag-wrap {
	background-color: #0052d9;
	color: #fff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	text-align: center;
}

.operations-btns {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.operations-btns_checkBox {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 28rpx;
	cursor: pointer;
}

.checkbox {
	width: 30rpx;
	height: 30rpx;
	border: 2rpx solid #ccc;
	border-radius: 4rpx;
	background-color: #fff;
}

.checkbox.checked {
	background-color: #0052d9;
	border-color: #0052d9;
}

.operations-timer {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.input-class {
	width: 120rpx;
	height: 60rpx;
	border: 1px solid #ccc;
	border-radius: 4rpx;
	text-align: center;
	font-size: 28rpx;
	padding: 0 10rpx;
}

.btn-small {
	background-color: #0052d9;
	color: #fff;
	padding: 15rpx 25rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	border: none;
}

.btn-primary {
	background-color: #0052d9;
}
</style>
