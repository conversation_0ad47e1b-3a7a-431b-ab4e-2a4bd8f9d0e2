page {
  padding: 15rpx;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
}

.nav-wrap {
  width: 100vw;
  --td-navbar-color: #fff;
  --td-navbar-bg-color: #0052d9;
}

.rec-textarea-wrap {
  display: block;
  padding: 30rpx;
  width: 100%;
  height: 30vh;
  font-size: 32rpx;
  border: 1px solid #ccc;
  border-radius: 8rpx;
  box-sizing: border-box;
  overflow-x: none;
  overflow-y: scroll;
  word-wrap: break-word;
  word-break: normal;
}

.rec-wrap-placeholder {
  font-size: 32rpx;
  font-weight: lighter;
  color: #999;
}

.send-textarea-wrap {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 8rpx;
}

.send-textarea-content {
  height: 30vh !important;
  font-size: 32rpx !important;
}

.operations-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
}

.operations-total {
  display: flex;
  flex-direction: column;
}

.operations-btns {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 15rpx;
  box-sizing: border-box;
}

.operations-btns_checkBox {
  margin-right: 30rpx;
}

.operations-btns_button {
  padding: 8rpx 12rpx !important;
  width: 120rpx !important;
  height: 60rpx !important;
  line-height: 60rpx !important;
}

.operations-timer {
  display: flex;
  align-items: center;
}

.input-class {
  margin-right: 15rpx;
  padding: 5rpx 10rpx !important;
  width: 100rpx;
  height: 50rpx;
  line-height: 50rpx;
  background-color: #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
}
