<!-- 导航栏 -->
<t-navbar
  class="nav-wrap"
  title="{{ deviceName }}"
  left-arrow
/>

<!-- 接收框 -->
<t-textarea
  t-class="rec-textarea-wrap"
  placeholder="接收框"
  disabled="{{true}}"
  value="{{recData}}"
/>

<view class="operations-wrap">
  <view class="operations-total">
    <t-tag theme="primary" variant="outline" style="margin-bottom: 5rpx">RX：{{recTotal}}</t-tag>
    <t-tag theme="primary" variant="outline">TX：{{sendTotal}}</t-tag>
  </view>
  <view class="operations-btns">
    <check-box
      wx:for="{{checkboxOptions}}"
      wx:key="index"
      label="{{item.label}}"
      checkValue="{{item.value}}"
      wrap-class="operations-btns_checkBox"
      data-index="{{index}}"
      bindchange="onHexChange"/>
    <t-button icon="clear" size="small" bindtap="cleanRecTextarea"/>
  </view>
</view>

<!-- 发送框 -->
<t-textarea
  t-class="send-textarea-wrap"
  t-class-textarea="send-textarea-content"
  placeholder="发送框"
  value="{{sendData}}"
  bindchange="onSendTextareaChange"
  disabled="{{isAutoSend}}" />

<view class="operations-wrap">
  <view class="operations-timer">
    <check-box
      label="循环发送"
      checkValue="{{false}}"
      wrap-class="operations-btns_checkBox"
      bindchange="onAutoSendToggle"/>
    <input
      class="input-class"
      type="number"
      value="{{interval}}"
      disabled="{{isAutoSend}}"
      bindinput="onIntervalChange"/>
    <text style="font-size: 30rpx">ms</text>
  </view>
  <view class="operations-btns">
    <t-button icon="clear" size="small" style="margin-right: 15rpx" bindtap="cleanSendTextarea"/>
    <t-button theme="primary" size="small" bindtap="onSendBtnTap">发 送</t-button>
  </view>
</view>
