<template>
	<view class="inspection-container">
		<view class="header">
			<text class="title">设备点检</text>
		</view>

		<view class="scan-area" v-if="!deviceInfo">
			<image class="scan-icon" src="/static/scan-icon.png"></image>
			<text class="scan-tip">请扫描设备二维码</text>
			<button class="scan-btn" @tap="scanQRCode">扫描二维码</button>
		</view>

		<view class="device-info" v-if="deviceInfo">
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">设备信息</text>
				</view>
				<view class="info-item">
					<text class="item-label">设备ID：</text>
					<text class="item-value">{{deviceInfo.id}}</text>
				</view>
				<view class="info-item">
					<text class="item-label">设备名称：</text>
					<text class="item-value">{{deviceInfo.name}}</text>
				</view>
				<view class="info-item">
					<text class="item-label">设备位置：</text>
					<text class="item-value">{{deviceInfo.location}}</text>
				</view>
				<view class="info-item">
					<text class="item-label">当前位置：</text>
					<text class="item-value">{{location.latitude}}, {{location.longitude}}</text>
				</view>
				<view class="info-item">
					<text class="item-label">扫描时间：</text>
					<text class="item-value">{{scanTime}}</text>
				</view>
			</view>

			<view class="status-selection" v-if="!submitted">
				<text class="selection-title">请选择设备状态：</text>
				<view class="btn-group">
					<button class="status-btn normal" @tap="selectStatus('normal')">正常</button>
					<button class="status-btn abnormal" @tap="selectStatus('abnormal')">异常</button>
				</view>

				<view class="abnormal-desc" v-if="status === 'abnormal'">
					<text class="desc-label">异常描述：</text>
					<textarea class="desc-input" v-model="abnormalDesc" placeholder="请输入异常描述（必填）"></textarea>
				</view>

				<button class="submit-btn" @tap="submitInspection" :disabled="submitDisabled">提交点检结果</button>
			</view>

			<view class="result-area" v-if="submitted">
				<view class="result-icon success" v-if="status === 'normal'">✓</view>
				<view class="result-icon error" v-else>!</view>
				<text class="result-text">点检结果已提交</text>
				<text class="result-status">状态：{{status === 'normal' ? '正常' : '异常'}}</text>
				<text class="result-desc" v-if="status === 'abnormal'">异常描述：{{abnormalDesc}}</text>
				<button class="back-btn" @tap="resetInspection">返回继续点检</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				deviceInfo: null,
				location: {
					latitude: '获取中...',
					longitude: '获取中...'
				},
				scanTime: '',
				status: '',
				abnormalDesc: '',
				submitted: false
			}
		},
		computed: {
			submitDisabled() {
				// 如果选择了异常状态但没有填写异常描述，则禁用提交按钮
				return this.status === 'abnormal' && !this.abnormalDesc.trim();
			}
		},
		onLoad() {
			// 获取当前位置
			this.getLocation();
		},
		methods: {
			// 获取当前位置
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.location = {
							latitude: res.latitude.toFixed(6),
							longitude: res.longitude.toFixed(6)
						};
					},
					fail: (err) => {
						console.error('获取位置失败', err);
						uni.showToast({
							title: '获取位置信息失败，请检查权限设置',
							icon: 'none',
							duration: 3000
						});
					}
				});
			},

			// 扫描二维码
			scanQRCode() {
				uni.scanCode({
					scanType: ['qrCode'],
					success: (res) => {
						try {
							// 假设二维码内容是JSON格式的设备信息
							// 实际项目中可能需要根据二维码内容去请求设备详情
							const deviceData = JSON.parse(res.result);
							this.deviceInfo = deviceData;

							// 记录扫描时间
							const now = new Date();
							this.scanTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

						} catch (e) {
							// 如果二维码内容不是有效的JSON，则模拟一些数据
							console.log('解析二维码数据失败，使用模拟数据', e);
							this.deviceInfo = {
								id: 'DB-' + Math.floor(Math.random() * 1000).toString().padStart(3, '0'),
								name: '配电箱 #' + Math.floor(Math.random() * 100),
								location: '3楼配电室'
							};

							// 记录扫描时间
							const now = new Date();
							this.scanTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
						}
					},
					fail: (err) => {
						console.error('扫码失败', err);
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'none'
						});
					}
				});
			},

			// 选择设备状态
			selectStatus(status) {
				this.status = status;
				if (status === 'normal') {
					this.abnormalDesc = '';
				}
			},

			// 提交点检结果
			submitInspection() {
				// 验证输入
				if (!this.status) {
					uni.showToast({
						title: '请选择设备状态',
						icon: 'none'
					});
					return;
				}

				if (this.status === 'abnormal' && !this.abnormalDesc.trim()) {
					uni.showToast({
						title: '请输入异常描述',
						icon: 'none'
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '提交中...'
				});

				// 构建提交数据
				const submitData = {
					deviceId: this.deviceInfo.id,
					status: this.status,
					location: this.location,
					scanTime: this.scanTime,
					description: this.status === 'abnormal' ? this.abnormalDesc : ''
				};

				// 模拟提交请求
				setTimeout(() => {
					uni.hideLoading();
					console.log('提交点检数据', submitData);

					// 标记为已提交
					this.submitted = true;

					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
				}, 1500);
			},

			// 重置点检，继续下一个
			resetInspection() {
				this.deviceInfo = null;
				this.status = '';
				this.abnormalDesc = '';
				this.submitted = false;
				this.scanTime = '';
			}
		}
	}
</script>

<style>
	.inspection-container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		margin-bottom: 40rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.scan-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 60rpx 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-top: 100rpx;
	}

	.scan-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
	}

	.scan-tip {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 50rpx;
	}

	.scan-btn {
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #1976D2;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 45rpx;
	}

	.device-info {
		margin-top: 30rpx;
	}

	.info-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 30rpx;
	}

	.card-header {
		border-bottom: 1rpx solid #eee;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.info-item {
		display: flex;
		margin-bottom: 16rpx;
	}

	.item-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
	}

	.item-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.status-selection {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.selection-title {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 30rpx;
		display: block;
	}

	.btn-group {
		display: flex;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.status-btn {
		width: 48%;
		height: 90rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 10rpx;
	}

	.normal {
		background-color: #4CAF50;
		color: #fff;
	}

	.abnormal {
		background-color: #F44336;
		color: #fff;
	}

	.abnormal-desc {
		margin-top: 30rpx;
		margin-bottom: 30rpx;
	}

	.desc-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.desc-input {
		width: 100%;
		height: 200rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333;
	}

	.submit-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #1976D2;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}

	.submit-btn[disabled] {
		background-color: #cccccc;
		color: #999999;
	}

	.result-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.result-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 60rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.success {
		background-color: #4CAF50;
		color: #fff;
	}

	.error {
		background-color: #F44336;
		color: #fff;
	}

	.result-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.result-status {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.result-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
		text-align: center;
	}

	.back-btn {
		width: 80%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #1976D2;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 45rpx;
	}
</style>
