<template>
	<view class="container">
		<view class="header">
			<text class="title">配置配电箱信息</text>
		</view>

		<view class="form-card">
			<!-- 项目选择 -->
			<view class="form-item" >
				<text class="form-label">项目</text>
				<picker @change="onProjectChange" :value="selectedProjectIndex" :range="projectList" range-key="name">
					<view class="picker-view">
						<text v-if="selectedProjectIndex >= 0">{{projectList[selectedProjectIndex].name}}</text>
						<text v-else class="placeholder">请选择项目</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>

			<!-- 地址选择 -->
			<view class="form-item" >
				<text class="form-label">地址</text>
				<picker @change="onAddressChange" :value="selectedAddressIndex" :range="addressList" range-key="name">
					<view class="picker-view">
						<text v-if="selectedAddressIndex >= 0">{{addressList[selectedAddressIndex].name}}</text>
						<text v-else class="placeholder">请选择地址</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>

			<!-- 经纬度设置 -->
			<view class="form-item">
				<text class="form-label">经度</text>
				<text class="form-value">{{location.longitude}}</text>
			</view>

			<view class="form-item">
				<text class="form-label">纬度</text>
				<text class="form-value">{{location.latitude}}</text>
			</view>

			<!-- 提交按钮 -->
			<view class="form-actions">
				<button class="btn-primary" @click="submitConfig">保存配置</button>
				<button class="btn-secondary" @click="goBack">取消</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				boxId: '',
				boxInfo: {},
				needConfigProject: false,
				needConfigAddress: false,
				groupId: 1,
				location: {
					latitude: 0,
					longitude: 0
				},

				// 项目列表
				projectList: [],
				selectedProjectIndex: -1,

				// 地址列表
				addressList: [],
				selectedAddressIndex: -1,

				// 加载状态
				loading: {
					project: false,
					address: false,
					submit: false
				}
			}
		},
		onLoad(options) {
			console.log('配置页面加载，接收到的参数:', options);

			// 从页面参数中获取配置数据
			if (options.data) {
				try {
					const data = JSON.parse(decodeURIComponent(options.data));
					console.log('解析后的配置数据:', data);

					this.boxId = data.boxId;
					this.boxInfo = data.boxInfo || {};
					this.needConfigProject = data.needConfigProject;
					this.needConfigAddress = data.needConfigAddress;
					this.groupId = data.groupId;
					this.location = data.location || { latitude: 0, longitude: 0 };

					console.log('设置的boxId:', this.boxId);
					console.log('设置的boxInfo:', this.boxInfo);
					console.log('是否需要配置项目:', this.needConfigProject);
					console.log('是否需要配置地址:', this.needConfigAddress);
					console.log('设置的groupId:', this.groupId);
					console.log('设置的location:', this.location);

					// 加载项目列表
					// 无论是否需要配置项目，都加载项目列表，确保能正确显示已有项目
					this.loadProjectList();

					// 加载地址列表
					// 无论是否有项目ID，都加载地址列表，确保地址选项始终可用
					if (this.needConfigAddress) {
						// 如果有项目ID，则加载该项目下的地址列表
						if (this.boxInfo.boxProjectId) {
							this.loadAddressListByProject(this.boxInfo.boxProjectId);
						} else {
							// 如果没有项目ID，则加载所有地址列表
							this.loadAddressList();
						}
					}
				} catch (e) {
					console.error('解析配置数据失败:', e);
					uni.showToast({
						title: '解析配置数据失败',
						icon: 'none'
					});
				}
			}
		},
		methods: {
			// 加载项目列表
			loadProjectList() {
				this.loading.project = true;

				// 构建API请求URL
				// 如果 groupId 为 null 或 undefined，则使用默认值 1
				const groupId = this.groupId || 1;
				console.log('加载项目列表，使用 groupId:', groupId);

				// 尝试使用不同的API路径
				// 首先尝试 /api/v1/boxProject
				const url = `/boxProject?groupId=${groupId}`;
				console.log('项目列表API URL:', url);

				// 调用API获取项目列表
				console.log('调用API获取项目列表:', url);
				this.$http.get(url)
					.then(res => {
						this.processProjectList(res);
					})
					.catch(err => {
						console.error('使用 /api/v1/boxProject 获取项目列表失败，尝试使用 /boxProject:', err);

						// 如果第一个API失败，尝试使用 /boxProject
						const fallbackUrl = `/boxProject?groupId=${groupId}`;
						console.log('尝试使用备用API URL:', fallbackUrl);

						this.$http.get(fallbackUrl)
							.then(res => {
								this.processProjectList(res);
							})
							.catch(err => {
								this.loading.project = false;
								console.error('获取项目列表失败:', err);

								uni.showToast({
									title: '获取项目列表失败',
									icon: 'none'
								});
							});
					});
			},

			// 处理项目列表响应
			processProjectList(res) {
				this.loading.project = false;
				console.log('获取项目列表成功:', res);
				console.log('项目列表数据类型:', typeof res);
				console.log('原始项目列表响应数据:', JSON.stringify(res));

				// 检查返回的数据结构
				let projectList = res;

				// 如果返回的是 res.content 结构
				if (res && res.content && Array.isArray(res.content)) {
					console.log('检测到 res.content 结构');
					projectList = res.content;
				}
				// 如果返回的是 res.data.content 结构
				else if (res && res.data && res.data.content && Array.isArray(res.data.content)) {
					console.log('检测到 res.data.content 结构');
					projectList = res.data.content;
				}

				console.log('处理后的项目列表:', projectList);
				console.log('项目列表是否为数组:', Array.isArray(projectList));

				if (projectList && projectList.length > 0) {
					console.log('项目列表第一项:', projectList[0]);
					console.log('项目列表长度:', projectList.length);
				}

				if (projectList && Array.isArray(projectList)) {
					// 检查项目列表中的项目是否有name属性
					const hasNameProperty = projectList.length > 0 && projectList[0].hasOwnProperty('name');
					console.log('项目列表中的项目是否有name属性:', hasNameProperty);

					if (!hasNameProperty && projectList.length > 0) {
						// 如果项目没有name属性，但有其他可能的名称属性，则进行转换
						console.log('项目列表中的项目没有name属性，尝试查找其他可能的名称属性');
						console.log('项目列表第一项的所有属性:', Object.keys(projectList[0]));

						// 尝试查找可能的名称属性
						const possibleNameProps = ['projectName', 'title', 'label', 'text', 'value'];
						let nameProperty = null;

						for (const prop of possibleNameProps) {
							if (projectList[0].hasOwnProperty(prop)) {
								nameProperty = prop;
								console.log(`找到可能的名称属性: ${prop}`);
								break;
							}
						}

						if (nameProperty) {
							// 转换项目列表，添加name属性
							this.projectList = projectList.map(item => ({
								...item,
								name: item[nameProperty]
							}));
							console.log('转换后的项目列表:', this.projectList);
						} else {
							// 如果找不到可能的名称属性，则使用id作为name
							this.projectList = projectList.map(item => ({
								...item,
								name: `项目 ${item.id || '未知'}`
							}));
							console.log('使用id作为name的项目列表:', this.projectList);
						}
					} else {
						// 如果项目有name属性，则直接使用
						this.projectList = projectList;
					}

					// 如果配电箱已有项目ID，则选中对应项目
					if (this.boxInfo.boxProjectId) {
						console.log('配电箱已有项目ID:', this.boxInfo.boxProjectId, '类型:', typeof this.boxInfo.boxProjectId);

						// 将 boxProjectId 转换为数字进行比较
						const boxProjectIdNum = Number(this.boxInfo.boxProjectId);

						// 查找匹配的项目，同时考虑字符串和数字类型的 ID
						const index = this.projectList.findIndex(item => {
							const itemIdNum = Number(item.id);
							return itemIdNum === boxProjectIdNum || item.id === this.boxInfo.boxProjectId;
						});

						console.log('找到匹配项目的索引:', index);
						if (index >= 0) {
							this.selectedProjectIndex = index;
							console.log('已选中项目:', this.projectList[index].name);
						}
					}
				}
			},

			// 加载地址列表
			loadAddressList() {
				this.loading.address = true;

				// 构建API请求URL
				// 如果 groupId 为 null 或 undefined，则使用默认值 1
				const groupId = this.groupId || 1;
				console.log('加载地址列表，使用 groupId:', groupId);
				const url = `/boxAddress?groupId=${groupId}`;
				console.log('地址列表API URL:', url);

				// 调用API获取地址列表
				console.log('调用API获取地址列表:', url);
				this.$http.get(url)
					.then(res => {
						this.loading.address = false;
						console.log('获取地址列表成功:', res);
						console.log('地址列表数据类型:', typeof res);

						// 检查返回的数据结构
						let addressList = res;

						// 如果返回的是 res.content 结构
						if (res && res.content && Array.isArray(res.content)) {
							console.log('检测到 res.content 结构');
							addressList = res.content;
						}
						// 如果返回的是 res.data.content 结构
						else if (res && res.data && res.data.content && Array.isArray(res.data.content)) {
							console.log('检测到 res.data.content 结构');
							addressList = res.data.content;
						}

						console.log('处理后的地址列表:', addressList);
						console.log('地址列表是否为数组:', Array.isArray(addressList));

						if (addressList && addressList.length > 0) {
							console.log('地址列表第一项:', addressList[0]);
							console.log('地址列表长度:', addressList.length);
						}

						if (addressList && Array.isArray(addressList)) {
							// 检查地址列表中的地址是否有name属性
							const hasNameProperty = addressList.length > 0 && addressList[0].hasOwnProperty('name');
							console.log('地址列表中的地址是否有name属性:', hasNameProperty);

							if (!hasNameProperty && addressList.length > 0) {
								// 如果地址没有name属性，使用 company / address 格式作为显示名称
								console.log('地址列表中的地址没有name属性，使用 company / address 格式');
								console.log('地址列表第一项的所有属性:', Object.keys(addressList[0]));

								// 使用 company / address 格式作为显示名称
								this.addressList = addressList.map(item => {
									// 检查是否有 company 和 address 属性
									const hasCompany = item.hasOwnProperty('company');
									const hasAddress = item.hasOwnProperty('address');

									let displayName = '';

									if (hasCompany && hasAddress) {
										// 如果同时有 company 和 address，使用 "company / address" 格式
										displayName = `${item.company} / ${item.address}`;
									} else if (hasCompany) {
										// 如果只有 company
										displayName = item.company;
									} else if (hasAddress) {
										// 如果只有 address
										displayName = item.address;
									} else {
										// 尝试查找可能的名称属性
										const possibleNameProps = ['addressName', 'title', 'label', 'text', 'value', 'address'];
										let nameProperty = null;

										for (const prop of possibleNameProps) {
											if (item.hasOwnProperty(prop)) {
												nameProperty = prop;
												break;
											}
										}

										if (nameProperty && item[nameProperty]) {
											// 如果有找到的名称属性
											displayName = item[nameProperty];
										} else {
											// 如果都没有，使用 ID
											displayName = `地址 ${item.id || '未知'}`;
										}
									}

									return {
										...item,
										name: displayName
									};
								});
								console.log('使用 company / address 格式的地址列表:', this.addressList);
							} else {
								// 如果地址有name属性，则直接使用
								this.addressList = addressList;
							}

							// 如果配电箱已有地址ID，则选中对应地址
							if (this.boxInfo.boxAddressId) {
								console.log('配电箱已有地址ID:', this.boxInfo.boxAddressId, '类型:', typeof this.boxInfo.boxAddressId);

								// 将 boxAddressId 转换为数字进行比较
								const boxAddressIdNum = Number(this.boxInfo.boxAddressId);

								// 查找匹配的地址，同时考虑字符串和数字类型的 ID
								const index = this.addressList.findIndex(item => {
									const itemIdNum = Number(item.id);
									return itemIdNum === boxAddressIdNum || item.id === this.boxInfo.boxAddressId;
								});

								console.log('找到匹配地址的索引:', index);
								if (index >= 0) {
									this.selectedAddressIndex = index;
									console.log('已选中地址:', this.addressList[index].name);
								}
							}
						}
					})
					.catch(err => {
						this.loading.address = false;
						console.error('获取地址列表失败:', err);

						uni.showToast({
							title: '获取地址列表失败',
							icon: 'none'
						});
					});
			},

			// 项目选择变更
			onProjectChange(e) {
				this.selectedProjectIndex = e.detail.value;

				// 重置地址选择
				this.selectedAddressIndex = -1;

				// 如果选择了项目，则加载该项目下的地址列表
				if (this.selectedProjectIndex >= 0) {
					const selectedProject = this.projectList[this.selectedProjectIndex];
					console.log('选择的项目:', selectedProject);

					// 显示加载中提示
					uni.showLoading({
						title: '加载地址列表...'
					});

					// 加载该项目下的地址列表
					this.loadAddressListByProject(selectedProject.id);

					// 显示提示
					uni.showToast({
						title: '已选择项目: ' + selectedProject.name,
						icon: 'none',
						duration: 1500
					});
				}
			},

			// 根据项目ID加载地址列表
			loadAddressListByProject(projectId) {
				this.loading.address = true;

				// 构建API请求URL
				// 如果 groupId 为 null 或 undefined，则使用默认值 1
				const groupId = this.groupId || 1;
				console.log('根据项目加载地址列表，使用 groupId:', groupId);
				console.log('根据项目加载地址列表，使用 projectId:', projectId);

				// 构建URL，如果有项目ID，则添加项目ID参数
				let url = `/boxAddress?groupId=${groupId}`;
				if (projectId) {
					url += `&projectId=${projectId}`;
				}
				console.log('项目下的地址列表API URL:', url);

				// 调用API获取地址列表
				console.log('调用API获取项目下的地址列表:', url);
				this.$http.get(url)
					.then(res => {
						this.loading.address = false;
						uni.hideLoading(); // 隐藏加载中提示
						console.log('获取项目下的地址列表成功:', res);
						console.log('地址列表数据类型:', typeof res);

						// 打印原始响应数据，用于调试
						console.log('原始地址列表响应数据:', JSON.stringify(res));

						// 检查返回的数据结构
						let addressList = res;

						// 如果返回的是 res.content 结构
						if (res && res.content && Array.isArray(res.content)) {
							console.log('检测到 res.content 结构');
							addressList = res.content;
						}
						// 如果返回的是 res.data.content 结构
						else if (res && res.data && res.data.content && Array.isArray(res.data.content)) {
							console.log('检测到 res.data.content 结构');
							addressList = res.data.content;
						}

						// 如果地址列表为空或不是数组，则创建一个空数组
						if (!addressList || !Array.isArray(addressList)) {
							console.log('地址列表为空或不是数组，创建空数组');
							addressList = [];
							this.addressList = [];
							return;
						}

						console.log('处理后的地址列表:', addressList);
						console.log('地址列表是否为数组:', Array.isArray(addressList));
						console.log('地址列表长度:', addressList.length);

						if (addressList.length > 0) {
							console.log('地址列表第一项:', addressList[0]);

							// 检查地址列表中的地址是否有name属性
							const hasNameProperty = addressList[0].hasOwnProperty('name');
							console.log('地址列表中的地址是否有name属性:', hasNameProperty);

							if (!hasNameProperty) {
								// 如果地址没有name属性，使用 company / address 格式作为显示名称
								console.log('地址列表中的地址没有name属性，使用 company / address 格式');
								console.log('地址列表第一项的所有属性:', Object.keys(addressList[0]));

								// 使用 company / address 格式作为显示名称
								this.addressList = addressList.map(item => {
									// 检查是否有 company 和 address 属性
									const hasCompany = item.hasOwnProperty('company');
									const hasAddress = item.hasOwnProperty('address');

									let displayName = '';

									if (hasCompany && hasAddress) {
										// 如果同时有 company 和 address，使用 "company / address" 格式
										displayName = `${item.company} / ${item.address}`;
									} else if (hasCompany) {
										// 如果只有 company
										displayName = item.company;
									} else if (hasAddress) {
										// 如果只有 address
										displayName = item.address;
									} else {
										// 尝试查找可能的名称属性
										const possibleNameProps = ['addressName', 'title', 'label', 'text', 'value', 'address'];
										let nameProperty = null;

										for (const prop of possibleNameProps) {
											if (item.hasOwnProperty(prop)) {
												nameProperty = prop;
												break;
											}
										}

										if (nameProperty && item[nameProperty]) {
											// 如果有找到的名称属性
											displayName = item[nameProperty];
										} else {
											// 如果都没有，使用 ID
											displayName = `地址 ${item.id || '未知'}`;
										}
									}

									return {
										...item,
										name: displayName
									};
								});
								console.log('使用 company / address 格式的地址列表:', this.addressList);
							} else {
								// 如果地址有name属性，则直接使用
								this.addressList = addressList;
								console.log('地址列表已有name属性，直接使用:', this.addressList);
							}
						} else {
							// 如果地址列表为空，则设置为空数组
							console.log('地址列表为空，设置为空数组');
							this.addressList = [];
						}

						// 如果配电箱已有地址ID，则选中对应地址
						if (this.boxInfo.boxAddressId) {
							console.log('配电箱已有地址ID:', this.boxInfo.boxAddressId, '类型:', typeof this.boxInfo.boxAddressId);

							// 将 boxAddressId 转换为数字进行比较
							const boxAddressIdNum = Number(this.boxInfo.boxAddressId);

							// 查找匹配的地址，同时考虑字符串和数字类型的 ID
							const index = this.addressList.findIndex(item => {
								const itemIdNum = Number(item.id);
								return itemIdNum === boxAddressIdNum || item.id === this.boxInfo.boxAddressId;
							});

							console.log('找到匹配地址的索引:', index);
							if (index >= 0) {
								this.selectedAddressIndex = index;
								console.log('已选中地址:', this.addressList[index].name);
							}
						}

						// 如果地址列表为空，显示提示
						if (this.addressList.length === 0) {
							uni.showToast({
								title: '该项目下没有可用地址',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						this.loading.address = false;
						uni.hideLoading(); // 隐藏加载中提示
						console.error('获取项目下的地址列表失败:', err);

						uni.showToast({
							title: '获取地址列表失败',
							icon: 'none'
						});

						// 设置为空数组
						this.addressList = [];
					});
			},

			// 地址选择变更
			onAddressChange(e) {
				this.selectedAddressIndex = e.detail.value;
			},

			// 提交配置
			submitConfig() {
				console.log('提交配置');
				console.log('当前项目索引:', this.selectedProjectIndex);
				console.log('当前地址索引:', this.selectedAddressIndex);
				console.log('项目列表:', this.projectList);
				console.log('地址列表:', this.addressList);

				// 验证表单
				// 从本地存储获取用户信息
				const userInfo = uni.getStorageSync('userInfo');
				const groupId = userInfo && userInfo.groupId ? userInfo.groupId : null;
				console.log('从用户信息中获取的groupId:', groupId);

				// 验证 groupId
				if (!groupId) {
					uni.showModal({
						title: '参数错误',
						content: '缺少租户ID (groupId)，无法保存配置。请重新登录后再试。',
						showCancel: false
					});
					return;
				}

				// 验证项目选择
				if (this.needConfigProject && this.selectedProjectIndex < 0) {
					uni.showModal({
						title: '请选择项目',
						content: '项目是必填项，请选择一个项目',
						showCancel: false
					});
					return;
				}

				// 验证地址选择
				if (this.needConfigAddress && this.selectedAddressIndex < 0) {
					uni.showModal({
						title: '请选择地址',
						content: '地址是必填项，请选择一个地址',
						showCancel: false
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '保存中...'
				});

				this.loading.submit = true;

				// 准备提交的数据
				console.log('提交配置，使用 groupId:', groupId);

				const submitData = {
					...this.boxInfo,
					groupId: groupId,
					userId: uni.getStorageSync('userInfo') && uni.getStorageSync('userInfo').id ? uni.getStorageSync('userInfo').id : null,
					longitude: this.location.longitude,
					latitude: this.location.latitude
				};

				// 如果需要配置项目，则添加项目相关信息
				if (this.needConfigProject && this.selectedProjectIndex >= 0) {
					const selectedProject = this.projectList[this.selectedProjectIndex];
					submitData.boxProjectId = selectedProject.id;

					// 添加 projectId 和 projectName
					submitData.projectId = selectedProject.id;
					submitData.projectName = selectedProject.name;

					console.log('选择的项目:', selectedProject);
					console.log('设置的项目ID:', submitData.projectId);
					console.log('设置的项目名称:', submitData.projectName);
				}

				// 如果需要配置地址，则添加地址相关信息
				if (this.needConfigAddress && this.selectedAddressIndex >= 0) {
					const selectedAddress = this.addressList[this.selectedAddressIndex];
					submitData.boxAddressId = selectedAddress.id;

					// 添加 addressId 和 addressName
					submitData.addressId = selectedAddress.id;
					submitData.addressName = selectedAddress.name;

					console.log('选择的地址:', selectedAddress);
					console.log('设置的地址ID:', submitData.addressId);
					console.log('设置的地址名称:', submitData.addressName);
				}

				// 构建API请求URL
				// 验证 boxId 是否存在
				if (!this.boxId) {
					console.error('boxId 不存在，无法构建 API URL');
					uni.hideLoading();
					uni.showModal({
						title: '参数错误',
						content: '缺少设备ID (boxId)，无法保存配置',
						showCancel: false
					});
					this.loading.submit = false;
					return;
				}

				console.log('使用的 boxId:', this.boxId);
				const url = `/distributionBox/${this.boxId}/position`;
				console.log('配电箱位置更新API URL:', url);

				// 准备位置信息数据
				const projectId = this.needConfigProject && this.selectedProjectIndex >= 0 ? this.projectList[this.selectedProjectIndex].id : null;
				const boxAddressId = this.needConfigAddress && this.selectedAddressIndex >= 0 ? this.addressList[this.selectedAddressIndex].id : null;

				// 验证必填参数
				if (!groupId) {
					uni.hideLoading();
					uni.showModal({
						title: '参数错误',
						content: '缺少租户ID (groupId)，无法保存配置',
						showCancel: false
					});
					this.loading.submit = false;
					return;
				}

				if (this.needConfigProject && !projectId) {
					uni.hideLoading();
					uni.showModal({
						title: '参数错误',
						content: '请选择项目',
						showCancel: false
					});
					this.loading.submit = false;
					return;
				}

				if (this.needConfigAddress && !boxAddressId) {
					uni.hideLoading();
					uni.showModal({
						title: '参数错误',
						content: '请选择地址',
						showCancel: false
					});
					this.loading.submit = false;
					return;
				}

				console.log('验证通过，准备提交数据');

				// 准备位置信息数据
				const positionData = {
					groupId: groupId
				};

				// 添加项目ID
				// 如果需要配置项目且用户选择了项目，则使用用户选择的项目ID
				// 否则，如果boxInfo中已有项目ID，则使用boxInfo中的项目ID
				if (this.needConfigProject && projectId) {
					positionData.projectId = projectId;
				} else if (this.boxInfo && this.boxInfo.boxProjectId) {
					positionData.projectId = this.boxInfo.boxProjectId;
				}

				// 添加地址ID
				// 如果需要配置地址且用户选择了地址，则使用用户选择的地址ID
				// 否则，如果boxInfo中已有地址ID，则使用boxInfo中的地址ID
				if (this.needConfigAddress && boxAddressId) {
					positionData.boxAddressId = boxAddressId;
				} else if (this.boxInfo && this.boxInfo.boxAddressId) {
					positionData.boxAddressId = this.boxInfo.boxAddressId;
				}

				console.log('提交的位置信息数据:', positionData);

				// 打印位置信息数据，用于调试
				console.log('提交的位置信息数据:', positionData);

				// 调用API更新配电箱位置信息
				this.$http.put(url, positionData)
					.then(res => {
						this.loading.submit = false;
						uni.hideLoading();
						console.log('更新配电箱位置信息成功:', res);

						uni.showToast({
							title: '位置信息保存成功',
							icon: 'success',
							duration: 2000,
							success: () => {
								// 延迟返回
								setTimeout(() => {
									this.goBack();
								}, 2000);
							}
						});
					})
					.catch(err => {
						this.loading.submit = false;
						uni.hideLoading();
						console.error('更新配电箱位置信息失败:', err);

						uni.showModal({
							title: '保存失败',
							content: '更新配电箱位置信息失败，请稍后重试',
							showCancel: false
						});
					});
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 30rpx;
		width: 100%;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.form-card {
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.form-header {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.form-value {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.picker-view {
		width: 100%;
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.placeholder {
		color: #999;
	}

	.picker-arrow {
		color: #999;
		font-size: 24rpx;
	}

	.picker-disabled {
		background-color: #f0f0f0;
		opacity: 0.7;
	}

	.picker-tip {
		font-size: 24rpx;
		color: #ff9800;
		margin-top: 10rpx;
		display: block;
	}

	.form-actions {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-top: 40rpx;
	}

	.btn-primary {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #1976D2;
		color: #fff;
	}

	.btn-secondary {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #f5f5f5;
		color: #333;
	}
</style>
