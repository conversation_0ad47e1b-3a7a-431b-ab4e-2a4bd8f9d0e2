<template>
	<view class="container">
		<view class="header">
			<text class="title">点检信息</text>
		</view>

		<view class="result-card">

			<view class="result-info">
				<view class="info-item">
					<text class="info-label">点检时间</text>
					<text class="info-value">{{inspectionData.time || '未知'}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">点检人</text>
					<text class="info-value">{{inspectionData.inspector || '未知'}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">SN</text>
					<text class="info-value">{{inspectionData.sn}}</text>
				</view>
			</view>

			<!-- 设备详细信息 -->
			<view class="device-info" v-if="inspectionData.boxInfo">
				<text class="section-title">设备详细信息</text>

				<view class="info-row" v-if="inspectionData.boxInfo.name">
					<text class="info-label">设备名称</text>
					<text class="info-value">{{inspectionData.boxInfo.name}}</text>
				</view>

				<view class="info-row" v-if="inspectionData.boxInfo.boxAddress">
					<text class="info-label">设备地址</text>
					<text class="info-value">{{inspectionData.boxInfo.boxAddress}}</text>
				</view>

				<view class="info-row" v-if="inspectionData.boxInfo.boxCategoryName">
					<text class="info-label">设备类型</text>
					<text class="info-value">{{inspectionData.boxInfo.boxCategoryName}}</text>
				</view>

				<view class="info-row" v-if="inspectionData.boxInfo.status !== undefined">
					<text class="info-label">设备状态</text>
					<text class="info-value">{{getDeviceStatusText(inspectionData.boxInfo.status)}}</text>
				</view>

			</view>

			<view class="location-info" v-if="inspectionData.location || (inspectionData.boxInfo && inspectionData.boxInfo.latitude && inspectionData.boxInfo.longitude)">
				<text class="section-title">位置信息</text>

				<view v-if="inspectionData.location">
					<text class="location-label">点检员位置：</text>
					<text class="location-value">经度: {{inspectionData.location.longitude}}</text>
					<text class="location-value">纬度: {{inspectionData.location.latitude}}</text>
				</view>

				<view v-if="inspectionData.boxInfo && inspectionData.boxInfo.latitude && inspectionData.boxInfo.longitude" class="box-location">
					<text class="location-label">配电箱位置：</text>
					<text class="location-value">经度: {{inspectionData.boxInfo.longitude}}</text>
					<text class="location-value">纬度: {{inspectionData.boxInfo.latitude}}</text>
				</view>

				<view v-if="distanceInfo.distance !== null" class="distance-info" :class="{'distance-abnormal': distanceInfo.isAbnormal}">
					<text class="distance-label">距离：</text>
					<text class="distance-value">{{Math.round(distanceInfo.distance)}}米</text>
					<text v-if="distanceInfo.isAbnormal" class="distance-warning">（超过距离阈值）</text>
				</view>
			</view>
		</view>

		<!-- 点检结果选择 - 仅在距离未超出阈值时显示 -->
		<view class="check-result-section" v-if="!distanceExceeded">
			<text class="section-title">点检结果</text>
			<view class="check-result-buttons">
				<button class="btn-result btn-normal" :class="{'btn-selected': checkResult === 1}" @click="selectCheckResult(1)">
					 正常
				</button>
				<button class="btn-result btn-abnormal" :class="{'btn-selected': checkResult === 2}" @click="selectCheckResult(2)">
					 异常
				</button>
			</view>

			<!-- 异常描述输入框，仅在选择异常或超出范围时显示 -->
			<view class="abnormal-description" v-if="checkResult === 2 || checkResult === 3">
				<text class="description-label">异常描述（必填）：</text>
				<textarea class="description-textarea" v-model="abnormalDescription" placeholder="请输入异常描述" maxlength="200" />
				<view class="textarea-counter">{{abnormalDescription.length}}/200</view>
			</view>
		</view>

		<!-- 距离超出阈值时显示异常描述输入框 -->
		<view class="check-result-section" v-if="distanceExceeded">
			<text class="section-title">点检结果</text>
			<view class="check-result-buttons">
				<button class="btn-result btn-abnormal btn-selected" disabled>
					 异常
				</button>
			</view>

			<view class="abnormal-description">
				<text class="description-label">异常描述：</text>
				<textarea class="description-textarea" v-model="abnormalDescription" placeholder="超出距离扫码" maxlength="200" />
				<view class="textarea-counter">{{abnormalDescription.length}}/200</view>
			</view>
		</view>

		<view class="footer">
			<!-- 提交按钮始终显示，无论距离是否超出阈值 -->
			<button class="btn-primary" @click="submitInspection">提交点检结果</button>
			<button class="btn-secondary" @click="goBack">返回点检列表</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inspectionData: {
					deviceName: '',
					deviceId: '',
					projectId: '',
					projectName: '',
					boxAddressId: '',
					boxAddress: '',
					status: 'success',
					time: '',
					inspector: '',
					location: null,
					boxInfo: null,
					remark: ''
				},
				// GPS距离信息
				distanceInfo: {
					distance: null, // 距离，单位：米
					isAbnormal: false, // 是否异常
					threshold: 1000 // 阈值，单位：米
				},
				// 是否正在编辑备注
				editingRemark: false,
				// 备注的临时存储
				tempRemark: '',
				// 点检结果：1=正常，2=异常，3=超出范围
				checkResult: 1,
				// 异常描述
				abnormalDescription: '',
				// 距离是否超出阈值
				distanceExceeded: false
			}
		},
		onLoad(options) {
			// 从页面参数中获取点检数据
			if (options.data) {
				try {
					const data = JSON.parse(decodeURIComponent(options.data));
					this.inspectionData = {
						...this.inspectionData,
						...data
					};
				} catch (e) {
					console.error('解析点检数据失败:', e);
				}
			}

			// 如果没有时间，设置为当前时间
			if (!this.inspectionData.time) {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');

				this.inspectionData.time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}

			// 如果没有点检人，从本地存储获取
			if (!this.inspectionData.inspector) {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.inspectionData.inspector = userInfo.name || '未知';
				}
			}

			// 处理boxInfo数据结构
			this.processBoxInfo();

			// 比较GPS距离
			this.compareGPSDistance();
		},
		methods: {
			// 获取设备状态文本
			getDeviceStatusText(status) {
				// 点检结果：1-正常，2-异常，3-超出范围
				switch (status) {
					case 0:
						return '未激活';
					case 1:
						return '正常';
					case 2:
						return '异常';
					case 3:
						return '超出范围';
					default:
						return '未知';
				}
			},

			// 提交点检结果
			submitInspection() {
				// 如果距离超出阈值，自动设置为异常点检，并设置异常描述
				if (this.distanceExceeded) {
					// 设置点检结果为异常
					this.checkResult = 2;
					// 设置异常描述
					this.abnormalDescription = '超出距离扫码';
				}

				uni.showLoading({
					title: '提交中...'
				});

				// 从本地存储获取token
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.hideLoading();
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}

				// 如果选择了异常或超出范围，但没有填写异常描述，则提示用户
				if ((this.checkResult === 2 || this.checkResult === 3) && !this.abnormalDescription.trim()) {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '请填写异常描述',
						showCancel: false
					});
					return;
				}

				// 重新查询配电箱信息，获取最新的项目和地址信息
				const deviceId = this.inspectionData.deviceId;
				const qrcode = this.inspectionData.qrcode;

				// 显示查询中提示
				uni.showLoading({
					title: '验证设备信息...'
				});

				// 构建API请求URL，优先使用qrcode查询
				let url = '';
				if (qrcode) {
					// 对qrcode进行URL编码，处理可能包含的特殊字符
					const encodedQrcode = encodeURIComponent(qrcode);
					console.log('原始qrcode:', qrcode);
					console.log('编码后的qrcode:', encodedQrcode);
					url = `/distributionBox?qrcode=${encodedQrcode}`;
				} else if (deviceId) {
					url = `/distributionBox/${deviceId}`;
				} else {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '设备信息不完整，无法提交点检结果',
						showCancel: false
					});
					return;
				}

				// 调用API获取最新的配电箱信息
				this.$http.get(url)
					.then(res => {
						// 处理API响应
						let latestBoxInfo = res;

						// 如果返回的是分页对象，提取第一个元素
						if (res && res.content && Array.isArray(res.content) && res.content.length > 0) {
							latestBoxInfo = res.content[0];
						}

						console.log('获取到最新的配电箱信息:', latestBoxInfo);

						// 检查项目和地址是否已设置
						const projectIdValue = Number(latestBoxInfo.boxProjectId);
						const addressIdValue = Number(latestBoxInfo.boxAddressId);

						const hasValidProjectId = !isNaN(projectIdValue) && projectIdValue > 0;
						const hasValidAddressId = !isNaN(addressIdValue) && addressIdValue > 0;

						// 如果已经有项目名称和地址名称，则认为已配置
						const hasProjectName = !!latestBoxInfo.projectName;
						const hasAddressName = !!latestBoxInfo.boxAddress;

						// 如果既没有有效的项目ID和地址ID，也没有项目名称和地址名称，则需要先配置
						if ((!hasValidProjectId || !hasValidAddressId) && (!hasProjectName || !hasAddressName)) {
							uni.hideLoading();

							// 从本地存储获取用户信息
							const userInfo = uni.getStorageSync('userInfo');
							const groupId = userInfo && userInfo.groupId ? userInfo.groupId : null;

							// 确定需要配置的内容
							const needConfigProject = !hasValidProjectId && !hasProjectName;
							const needConfigAddress = !hasValidAddressId && !hasAddressName;

							uni.showModal({
								title: '配置设备信息',
								content: `提交点检结果前，需要先配置设备的${needConfigProject ? '项目' : ''}${needConfigProject && needConfigAddress ? '和' : ''}${needConfigAddress ? '地址' : ''}，是否现在配置？`,
								confirmText: '立即配置',
								cancelText: '取消',
								success: (res) => {
									if (res.confirm) {
										// 用户选择立即配置
										this.showConfigForm(needConfigProject, needConfigAddress, groupId);
									}
								}
							});
							return;
						}

						// 更新本地的boxInfo
						this.inspectionData.boxInfo = latestBoxInfo;

						// 继续提交点检结果
						this.continueSubmitInspection();
					})
					.catch(err => {
						uni.hideLoading();
						console.error('获取配电箱信息失败:', err);

						uni.showModal({
							title: '提示',
							content: '获取设备信息失败，无法验证项目和地址配置，是否继续提交？',
							confirmText: '继续提交',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									// 用户选择继续提交
									this.continueSubmitInspection();
								}
							}
						});
					});

			},

			// 继续提交点检结果
			continueSubmitInspection() {
				uni.showLoading({
					title: '提交中...'
				});

				// 准备提交的数据，按照新的API格式
				// 获取qrcode并进行URL编码
				const qrcode = this.inspectionData.qrcode;
				console.log('提交点检结果，原始qrcode:', qrcode);

				const submitData = {
					qrcode: qrcode, // 使用配电箱的SN作为qrcode，API会自动处理编码
					longitude: this.inspectionData.location && this.inspectionData.location.longitude ? this.inspectionData.location.longitude : 0,
					latitude: this.inspectionData.location && this.inspectionData.location.latitude ? this.inspectionData.location.latitude : 0,
					checkResult: this.checkResult, // 使用用户选择的点检结果
					attachment: (this.checkResult === 2 || this.checkResult === 3) ? this.abnormalDescription : (this.inspectionData.remark || '点检正常')
				};

				// 打印提交的数据，用于调试
				console.log('提交点检结果数据:', submitData);

				// 调用API提交点检结果
				this.$http.post('/boxCheckRecord', submitData)
					.then(res => {
						uni.hideLoading();
						console.log('提交点检结果成功:', res);

						uni.showToast({
							title: '点检提交成功',
							icon: 'success',
							duration: 2000,
							success: () => {
								// 延迟返回点检列表
								setTimeout(() => {
									this.goBack();
								}, 2000);
							}
						});
					})
					.catch(err => {
						uni.hideLoading();
						console.error('提交点检结果失败:', err);

						// 显示详细错误信息
						let errorMsg = '提交点检结果失败，请稍后重试';
						if (err.data && err.data.message) {
							errorMsg = err.data.message;
						}

						uni.showModal({
							title: '提交失败',
							content: errorMsg,
							showCancel: false
						});
					});
			},

			// 返回点检列表
			goBack() {
				// 返回点检列表页面
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			// 编辑备注
			editRemark() {
				// 保存当前备注，以便取消时恢复
				this.tempRemark = this.inspectionData.remark || '';
				this.editingRemark = true;
			},

			// 保存备注
			saveRemark() {
				// 直接使用当前的备注内容
				this.editingRemark = false;

				uni.showToast({
					title: '备注已保存',
					icon: 'success',
					duration: 1500
				});
			},

			// 取消编辑备注
			cancelEditRemark() {
				// 恢复原来的备注
				this.inspectionData.remark = this.tempRemark;
				this.editingRemark = false;
			},

			// 选择点检结果
			selectCheckResult(result) {

				// 设置点检结果
				this.checkResult = result;

				// 如果选择正常，则清空异常描述
				if (result === 1) {
					this.abnormalDescription = '';
				}

				// 更新点检状态
				this.inspectionData.status = result === 1 ? 'success' : 'fail';

				// 添加震动反馈（如果设备支持）
				if (uni.vibrateShort) {
					uni.vibrateShort();
				}

				// 显示提示
				let toastTitle = '';
				switch(result) {
					case 1:
						toastTitle = '已选择：正常';
						break;
					case 2:
						toastTitle = '已选择：异常';
						break;
					case 3:
						toastTitle = '已选择：超出范围';
						break;
					default:
						toastTitle = '已选择：未知状态';
				}

				uni.showToast({
					title: toastTitle,
					icon: 'none',
					duration: 1500
				});

				// 如果选择了异常或超出范围，并且异常描述为空，则自动聚焦到异常描述输入框
				if ((result === 2 || result === 3) && !this.abnormalDescription) {
					// 使用setTimeout确保DOM已更新
					setTimeout(() => {
						// 这里无法直接聚焦，但可以提示用户
						uni.showToast({
							title: '请填写异常描述',
							icon: 'none',
							duration: 2000
						});
					}, 1500);
				}
			},

			// 计算两个GPS坐标之间的距离（单位：米）
			calculateDistance(lat1, lon1, lat2, lon2) {
				if (!lat1 || !lon1 || !lat2 || !lon2) {
					return null;
				}

				const R = 6371000; // 地球半径，单位：米
				const dLat = this.deg2rad(lat2 - lat1);
				const dLon = this.deg2rad(lon2 - lon1);
				const a =
					Math.sin(dLat/2) * Math.sin(dLat/2) +
					Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
					Math.sin(dLon/2) * Math.sin(dLon/2);
				const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
				const distance = R * c; // 距离，单位：米

				return distance;
			},

			// 角度转弧度
			deg2rad(deg) {
				return deg * (Math.PI/180);
			},

			// 处理boxInfo数据结构
			processBoxInfo() {
				// 检查boxInfo是否存在
				if (!this.inspectionData.boxInfo) {
					console.log('boxInfo不存在，无需处理');
					return;
				}

				console.log('处理前的boxInfo:', JSON.stringify(this.inspectionData.boxInfo));
				this.inspectionData.deviceId = this.inspectionData.boxInfo.id;
				console.log('deviceId:', this.inspectionData.deviceId);

				// 检查boxInfo是否是分页对象
				if (this.inspectionData.boxInfo.content && Array.isArray(this.inspectionData.boxInfo.content) && this.inspectionData.boxInfo.content.length > 0) {
					console.log('boxInfo是分页对象，提取第一个元素');

					// 保存原始boxInfo以备不时之需
					const originalBoxInfo = this.inspectionData.boxInfo;

					// 提取content数组的第一个元素作为新的boxInfo
					this.inspectionData.boxInfo = this.inspectionData.boxInfo.content[0];

					// 如果新的boxInfo没有sn，但原始数据中有，则保留
					if (!this.inspectionData.boxInfo.sn && originalBoxInfo.sn) {
						this.inspectionData.boxInfo.sn = originalBoxInfo.sn;
					}


					// 如果新的boxInfo没有qrcode，但原始数据中有，则保留
					if (!this.inspectionData.boxInfo.qrcode && originalBoxInfo.qrcode) {
						this.inspectionData.boxInfo.qrcode = originalBoxInfo.qrcode;
					}

					console.log('处理后的boxInfo:', JSON.stringify(this.inspectionData.boxInfo));
				} else {
					console.log('boxInfo不是分页对象，无需处理');
				}

				// 确保boxInfo中的boxProjectId和boxAddressId存在
				if (this.inspectionData.boxInfo) {
					// 从boxInfo中提取sn到inspectionData
					if (this.inspectionData.boxInfo.sn && !this.inspectionData.sn) {
						this.inspectionData.sn = this.inspectionData.boxInfo.sn;
					}

					// 从boxInfo中提取qrcode到inspectionData
					if (this.inspectionData.boxInfo.qrcode && !this.inspectionData.qrcode) {
						this.inspectionData.qrcode = this.inspectionData.boxInfo.qrcode;
					}
				}
			},

			// 比较GPS距离
			compareGPSDistance() {
				// 获取点检员手机的GPS信息
				const phoneLocation = this.inspectionData.location;

				// 获取配电箱返回的GPS信息
				const boxInfo = this.inspectionData.boxInfo;
				const boxLocation = {
					latitude: boxInfo && boxInfo.latitude ? boxInfo.latitude : null,
					longitude: boxInfo && boxInfo.longitude ? boxInfo.longitude : null
				};

				// 如果点检员手机没有GPS信息，则使用当前位置
				if (!phoneLocation || !phoneLocation.latitude || !phoneLocation.longitude) {
					console.log('点检员手机没有GPS信息，获取当前位置');
					this.getCurrentLocation().then(location => {
						this.inspectionData.location = location;
						// 重新比较GPS距离
						this.compareGPSDistance();
					}).catch(err => {
						console.error('获取当前位置失败:', err);
						uni.showToast({
							title: '获取当前位置失败',
							icon: 'none'
						});
					});
					return;
				}

				// 检查配电箱是否已配置经纬度
				if (!boxLocation.latitude || !boxLocation.longitude ||
					boxLocation.latitude === 0 || boxLocation.longitude === 0 ||
					boxLocation.latitude === '0' || boxLocation.longitude === '0') {
					console.log('配电箱未配置经纬度或经纬度为0，需要配置');
					console.log('配电箱经度:', boxLocation.longitude, '类型:', typeof boxLocation.longitude);
					console.log('配电箱纬度:', boxLocation.latitude, '类型:', typeof boxLocation.latitude);

					// 检查是否需要配置项目和地址
					this.checkAndConfigBoxInfo();
					return;
				}

				// 打印配电箱经纬度信息，用于调试
				console.log('配电箱经度:', boxLocation.longitude, '类型:', typeof boxLocation.longitude);
				console.log('配电箱纬度:', boxLocation.latitude, '类型:', typeof boxLocation.latitude);

				// 计算距离
				const distance = this.calculateDistance(
					phoneLocation.latitude,
					phoneLocation.longitude,
					boxLocation.latitude,
					boxLocation.longitude
				);

				// 如果无法计算距离，则不进行比较
				if (distance === null) {
					console.log('无法计算距离，不进行比较');
					return;
				}

				// 更新距离信息
				this.distanceInfo.distance = distance;

				// 判断是否超出范围
				if (distance > this.distanceInfo.threshold) {
					this.distanceInfo.isAbnormal = true;

					// 设置距离超出阈值标志
					this.distanceExceeded = true;

					// 更新点检状态和备注
					this.inspectionData.status = 'fail';
					this.inspectionData.remark = `超出范围：距离过远（${Math.round(distance)}米，超过${this.distanceInfo.threshold}米）`;

					// 自动设置点检结果为"异常"
					this.checkResult = 2;
					this.abnormalDescription = '超出距离扫码';

					// 显示提示
					uni.showModal({
						title: '超出范围',
						content: `您当前位置与配电箱位置相距${Math.round(distance)}米，超过阈值${this.distanceInfo.threshold}米。点击提交按钮将自动设置为异常点检，异常描述为"超出距离扫码"。`,
						showCancel: false
					});
				} else {
					console.log(`距离正常：${Math.round(distance)}米，未超过阈值${this.distanceInfo.threshold}米`);

					// 检查配电箱是否有经纬度信息
					const boxInfo = this.inspectionData.boxInfo;
					const hasLocation = boxInfo && boxInfo.latitude && boxInfo.longitude &&
						boxInfo.latitude !== 0 && boxInfo.longitude !== 0 &&
						boxInfo.latitude !== '0' && boxInfo.longitude !== '0';

					// 只有在配电箱没有经纬度信息的情况下才检查是否需要配置
					if (!hasLocation) {
						console.log('配电箱没有经纬度信息，检查是否需要配置项目和地址');
						this.checkAndConfigBoxInfo();
					} else {
						console.log('配电箱已有经纬度信息，无需配置');
					}
				}
			},

			// 获取当前位置
			getCurrentLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02', // 使用国测局坐标系
						success: (res) => {
							console.log('获取当前位置成功:', res);
							resolve({
								latitude: res.latitude,
								longitude: res.longitude
							});
						},
						fail: (err) => {
							console.error('获取当前位置失败:', err);
							reject(err);
						}
					});
				});
			},

			// 检查并配置配电箱信息（项目和地址）
			checkAndConfigBoxInfo() {
				const boxInfo = this.inspectionData.boxInfo;

				// 打印完整的boxInfo，用于调试
				console.log('检查配置配电箱信息，完整的boxInfo:', JSON.stringify(boxInfo));

				// 如果boxInfo为空，则不需要配置
				if (!boxInfo) {
					console.log('boxInfo为空，无法检查配置状态');
					return;
				}

				// 从本地存储获取用户信息
				const userInfo = uni.getStorageSync('userInfo');
				const groupId = userInfo && userInfo.groupId ? userInfo.groupId : null;
				console.log('检查配置配电箱信息，用户信息:', userInfo);
				console.log('检查配置配电箱信息，使用 groupId:', groupId);

				// 检查projectId和addressId是否存在且有效
				// 将值转换为数字进行比较，确保能正确处理字符串类型的数字
				const projectIdValue = Number(boxInfo.boxProjectId);
				const addressIdValue = Number(boxInfo.boxAddressId);

				// 检查转换后的数字是否大于0且不是NaN
				const hasValidProjectId = !isNaN(projectIdValue) && projectIdValue > 0;
				const hasValidAddressId = !isNaN(addressIdValue) && addressIdValue > 0;

				console.log('是否有有效的项目ID:', hasValidProjectId);
				console.log('是否有有效的地址ID:', hasValidAddressId);

				const needConfigProject = !hasValidProjectId;
				const needConfigAddress = !hasValidAddressId;

				// 如果已经有项目名称和地址名称，则认为已配置
				if (boxInfo.projectName && boxInfo.boxAddress) {
					console.log('配电箱已有项目名称和地址名称，认为已配置');
					console.log('项目名称:', boxInfo.projectName);
					console.log('地址名称:', boxInfo.boxAddress);
					return;
				}

				// 如果已经配置了项目和地址，则不需要配置
				if (!needConfigProject && !needConfigAddress) {
					console.log('配电箱已配置项目和地址，无需配置');
					console.log('有效的项目ID:', boxInfo.boxProjectId);
					console.log('有效的地址ID:', boxInfo.boxAddressId);
					return;
				}

				// 如果配电箱已经有经纬度信息，也不需要配置
				if (boxInfo.latitude && boxInfo.longitude &&
					boxInfo.latitude !== 0 && boxInfo.longitude !== 0 &&
					boxInfo.latitude !== '0' && boxInfo.longitude !== '0') {
					console.log('配电箱已有经纬度信息，无需配置');
					return;
				}

				// 显示配置对话框
				uni.showModal({
					title: '配置配电箱信息',
					content: `配电箱需要配置${needConfigProject ? '项目' : ''}${needConfigProject && needConfigAddress ? '和' : ''}${needConfigAddress ? '地址' : ''}，是否现在配置？`,
					confirmText: '立即配置',
					cancelText: '稍后配置',
					success: (res) => {
						if (res.confirm) {
							// 用户选择立即配置
							this.showConfigForm(needConfigProject, needConfigAddress, groupId);
						}
					}
				});
			},

			// 显示配置表单
			showConfigForm(needConfigProject, needConfigAddress, groupId) {
				// 创建配置页面数据
				// 确保 boxId 正确传递
				const deviceId = this.inspectionData.deviceId || this.inspectionData.boxInfo.id;

				console.log('传递给配置页面的设备ID:', deviceId);
				console.log('设备信息:', this.inspectionData.boxInfo);

				const configData = {
					boxId: deviceId, // 确保使用正确的设备ID
					boxInfo: this.inspectionData.boxInfo,
					needConfigProject: needConfigProject,
					needConfigAddress: needConfigAddress,
					groupId: groupId,
					location: this.inspectionData.location
				};

				// 将配置数据转为URL参数
				const dataParam = encodeURIComponent(JSON.stringify(configData));

				// 跳转到配置页面
				uni.navigateTo({
					url: `/pages/inspection/config?data=${dataParam}`
				});
			}
		}
	}
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 30rpx;
		width: 100%;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.result-card {
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.result-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.result-status {
		font-size: 26rpx;
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
	}

	.status-success {
		background-color: #e6f7e6;
		color: #07c160;
	}

	.status-fail {
		background-color: #ffe6e6;
		color: #f44336;
	}

	.result-info {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.device-info {
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
	}

	.info-label {
		font-size: 28rpx;
		color: #666;
	}

	.info-value {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.location-info {
		padding-bottom: 20rpx;
		margin-bottom: 30rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.location-label {
		font-size: 28rpx;
		color: #666;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.location-value {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
		padding-left: 20rpx;
	}

	.box-location {
		margin-top: 20rpx;
	}

	.distance-info {
		margin-top: 20rpx;
		padding: 15rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.distance-abnormal {
		background-color: #ffe6e6;
	}

	.distance-label {
		font-size: 28rpx;
		color: #666;
		font-weight: bold;
	}

	.distance-value {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
		margin-left: 10rpx;
	}

	.distance-warning {
		font-size: 28rpx;
		color: #f44336;
		margin-left: 10rpx;
	}

	.remark-section {
		padding-bottom: 20rpx;
	}

	.remark-content {
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
		display: block;
		margin-bottom: 20rpx;
	}

	.remark-textarea {
		width: 100%;
		height: 200rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.textarea-counter {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		margin-bottom: 20rpx;
	}

	.remark-actions {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx;
		margin-bottom: 20rpx;
	}

	.btn-small {
		padding: 10rpx 30rpx;
		font-size: 26rpx;
		background-color: #1976D2;
		color: #fff;
		border-radius: 8rpx;
	}

	.btn-cancel {
		background-color: #f5f5f5;
		color: #333;
	}

	.btn-edit {
		font-size: 26rpx;
		color: #1976D2;
		background: none;
		border: none;
		padding: 0;
		margin: 0;
		display: inline-block;
	}

	.check-result-section {
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 40rpx;
	}

	.check-result-buttons {
		display: flex;
		justify-content: space-between;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.btn-result {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: 2rpx solid transparent;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.btn-icon {
		margin-right: 10rpx;
		font-size: 36rpx;
	}

	.btn-normal {
		background-color: #f5f5f5;
		color: #333;
		border-color: #ddd;
	}

	.btn-abnormal {
		background-color: #f5f5f5;
		color: #333;
		border-color: #ddd;
	}

	.btn-normal.btn-selected {
		background-color: #e6f7e6;
		color: #07c160;
		border-color: #07c160;
		border-width: 4rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.btn-abnormal.btn-selected {
		background-color: #ffe6e6;
		color: #f44336;
		border-color: #f44336;
		border-width: 4rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.distance-exceeded-section {
		width: 100%;
		margin-bottom: 40rpx;
	}

	.distance-exceeded-card {
		width: 100%;
		background-color: #ffe6e6;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.distance-exceeded-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #f44336;
		margin-bottom: 20rpx;
		display: block;
	}

	.distance-exceeded-text {
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
		margin-bottom: 10rpx;
		display: block;
	}

	.btn-out-of-range {
		background-color: #f5f5f5;
		color: #333;
		border-color: #ddd;
	}

	.btn-out-of-range.btn-selected {
		background-color: #fff3e0;
		color: #ff9800;
		border-color: #ff9800;
		border-width: 4rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.abnormal-description {
		margin-top: 20rpx;
	}

	.description-label {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}

	.description-textarea {
		width: 100%;
		height: 200rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 20rpx;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.footer {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-top: 20rpx;
	}

	.btn-primary {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #1976D2;
		color: #fff;
	}

	.btn-secondary {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 10rpx;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #f5f5f5;
		color: #333;
	}
</style>
