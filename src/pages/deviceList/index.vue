<template>
	<view class="page">
		<view class="deviceList-header">
			<text>附近设备 ({{ devicesList.length }})</text>
		</view>

		<!-- 使用 uni-app 的下拉刷新组件 -->
		<scroll-view
			class="deviceList-content"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="handlePullDownRefresh"
			@refresherrestore="onRefreshRestore"
		>
			<!-- 调试信息 -->
			<view class="debug-info" style="background: #f0f0f0; padding: 20rpx; margin-bottom: 20rpx; font-size: 24rpx;">
				<text>设备数量: {{ devicesList.length }}</text><br>
				<text>搜索状态: {{ refreshing ? '搜索中...' : '已停止' }}</text><br>
				<text>蓝牙状态: {{ bluetoothState ? '已开启' : '未开启' }}</text>
			</view>

			<!-- 空状态提示 -->
			<view v-if="devicesList.length === 0" class="empty-tip">
				<text>{{ refreshing ? '正在搜索附近的蓝牙设备...' : '未发现蓝牙设备，请下拉刷新重试' }}</text>
			</view>

			<!-- 设备卡片列表 -->
			<view
				v-for="(device, index) in validDevicesList"
				:key="device.deviceId || index"
				class="deviceCard-wrap"
				:data-device-index="index"
				@click="onTapDeviceByIndex"
			>
				<view class="device-info">
					<view class="device-name">
						<view class="name">{{ device.name || device.localName || 'Unknown Device' }}</view>
						<view v-if="device.beacon" class="beacon">iBeacon</view>
					</view>
					<view class="device-id">DeviceId：{{ device.deviceId }}</view>
					<view class="device-id">UUID: {{ (device.advertisServiceUUIDs && device.advertisServiceUUIDs[0]) || null }}</view>
				</view>
				<view class="device-rssi">RSSI：{{ device.RSSI }}</view>
			</view>

			<!-- 底部间距 -->
			<view style="height: 120rpx"></view>
		</scroll-view>
	</view>
</template>

<script>
// 暂时移除外部依赖，确保页面能正常加载
// import {
// 	transToQuery,
// 	checkWxVerison,
// 	checkGPSState,
// 	checkBeacon,
// 	handleErrno
// } from '@/utils/index'
// import {
// 	GeneralServiceUUID,
// 	GeneralCharacteristicUUID,
// 	HC02ServiceUUID,
// 	HC02CharacteristicWriteUUID
// } from '@/configs/index'

// 蓝牙服务和特征值常量（暂时不使用）
// const GeneralServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB'
// const GeneralCharacteristicUUID = '0000FFE1-0000-1000-8000-00805F9B34FB'
// const HC02ServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB'
// const HC02CharacteristicWriteUUID = '0000FFE1-0000-1000-8000-00805F9B34FB'

export default {
	data() {
		return {
			refreshing: false,
			devicesList: [],
			services: [],
			loadingProps: { size: '50rpx' },
			bluetoothState: false, // 蓝牙状态
			// 私有变量移到 data 中
			_deviceIdsList: [],
			_discoveryTimer: null,
			_discoveryTimeout: 30000 // 搜索设备超时时间，单位ms
		}
	},

	computed: {
		// 过滤有效的设备列表
		validDevicesList() {
			console.log('计算 validDevicesList，原始 devicesList:', this.devicesList);
			const validDevices = this.devicesList.filter((device, index) => {
				console.log(`检查设备 ${index}:`, device);
				// 确保设备对象存在且有 deviceId
				const isValid = device && device.deviceId;
				console.log(`设备 ${index} 是否有效:`, isValid);
				return isValid;
			});
			console.log('过滤后的有效设备列表:', validDevices);
			return validDevices;
		}
	},

	onLoad() {
		console.log('deviceList 页面 onLoad');

		// 先初始化系统信息
		// #ifdef MP-WEIXIN
		try {
			const sysInfo = wx.getSystemInfoSync();
			wx.setStorageSync('sysInfo', sysInfo);
			console.log('系统信息初始化成功:', sysInfo);
		} catch (e) {
			console.error('系统信息初始化失败:', e);
		}
		// #endif

		console.log('onLoad 完成');
	},

	async onShow() {
		console.log('deviceList 页面 onShow');

		// 初始化小程序蓝牙
		// #ifdef MP-WEIXIN
		console.log('开始初始化蓝牙适配器...');
		wx.openBluetoothAdapter({
			success: () => {
				console.log('蓝牙适配器初始化成功');
				wx.setStorageSync('bluetoothAdapterState', true);
				// 不在这里设置 bluetoothState，让 onDevicesDiscovery 中的状态检查来设置
			},
			fail: (err) => {
				console.error('蓝牙适配器初始化失败:', err);
				wx.setStorageSync('bluetoothAdapterState', false);
				// 不在这里设置 bluetoothState，让 onDevicesDiscovery 中的状态检查来设置
				console.log('蓝牙可能已经初始化，继续执行');
			},
			complete: () => {
				console.log('蓝牙适配器初始化完成，设置监听器');
				// 监听蓝牙适配器
				wx.offBluetoothAdapterStateChange();
				wx.onBluetoothAdapterStateChange(this.handleBluetoothAdapterStateChange);

				// 监听蓝牙连接
				wx.offBLEConnectionStateChange();
				wx.onBLEConnectionStateChange(this.handleBLEConnectionStateChange);

				// 立即检查一次蓝牙状态，更新页面显示
				setTimeout(() => {
					console.log('检查蓝牙状态并更新页面显示');
					this.checkBluetoothState();

					// 再延迟一点开始搜索
					setTimeout(() => {
						console.log('开始执行设备搜索');
						this.onDevicesDiscovery();
					}, 200);
				}, 300);
			}
		});
		// #endif

		// #ifndef MP-WEIXIN
		console.log('非微信小程序环境，无法使用蓝牙功能');
		this.bluetoothState = false;
		uni.showModal({
			title: '提示',
			content: '蓝牙功能仅在微信小程序中可用',
			showCancel: false
		});
		// #endif
	},

	onHide() {
		clearTimeout(this._discoveryTimer);
		// #ifdef MP-WEIXIN
		wx.offBluetoothDeviceFound();
		wx.stopBluetoothDevicesDiscovery();
		// #endif
	},

	methods: {

		// 检查蓝牙状态（独立方法）
		checkBluetoothState() {
			console.log('检查蓝牙适配器状态...');
			// #ifdef MP-WEIXIN
			wx.getBluetoothAdapterState({
				success: (res) => {
					console.log('蓝牙适配器状态检查结果:', res);
					// 修复：使用正确的数据结构 res.adapterState.available
					const available = res.adapterState ? res.adapterState.available : res.available;
					this.bluetoothState = available;
					console.log('页面蓝牙状态已更新为:', this.bluetoothState);
				},
				fail: (err) => {
					console.error('获取蓝牙状态失败:', err);
					// 更新页面显示的蓝牙状态
					this.bluetoothState = false;
					console.log('页面蓝牙状态已更新为:', this.bluetoothState);
				}
			});
			// #endif

			// #ifndef MP-WEIXIN
			this.bluetoothState = false;
			console.log('非微信环境，页面蓝牙状态设为:', this.bluetoothState);
			// #endif
		},

		// 开启搜索附近设备
		onDevicesDiscovery() {
			console.log('开始搜索蓝牙设备...');
			// #ifdef MP-WEIXIN

			// 直接检查当前蓝牙状态，不依赖存储
			this.checkBluetoothState();

			wx.getBluetoothAdapterState({
				success: (res) => {
					console.log('搜索前蓝牙适配器状态检查结果:', res);
					// 修复：使用正确的数据结构
					const available = res.adapterState ? res.adapterState.available : res.available;
					console.log('解析后的蓝牙可用状态:', available);

					if (available) {
						console.log('蓝牙可用，开始搜索设备');
						this.startBluetoothDiscovery();
					} else {
						console.log('蓝牙不可用');
						uni.showToast({
							title: '请先开启蓝牙',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.error('获取蓝牙状态失败:', err);
					// 如果获取状态失败，尝试直接搜索
					console.log('状态检查失败，尝试直接搜索');
					this.startBluetoothDiscovery();
				}
			});
			// #endif
		},

		// 开始蓝牙设备搜索
		startBluetoothDiscovery() {
			console.log('执行蓝牙设备搜索...');

			// 先确保停止之前的搜索
			this.offDevicesDiscovery();

			// 等待一小段时间确保停止完成
			setTimeout(() => {
				wx.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: true, // 重复上报设备
					powerLevel: 'height', // 使用原始版本的参数
					interval: 1000,
					success: () => {
						console.log('开始搜索蓝牙设备成功');
						// 清空设备列表
						this._deviceIdsList = [];
						this.devicesList = [];
						console.log('已清空设备列表，开始监听设备发现事件');

						// 发现设备
						wx.onBluetoothDeviceFound(this.handleFoundBluetoothDevices);
						console.log('已设置设备发现监听器');

						// 超时关闭搜索
						const timeoutMs = this._discoveryTimeout || 30000; // 确保有默认值
						console.log('准备设置搜索超时，超时时间:', timeoutMs);
						this._discoveryTimer = setTimeout(() => {
							console.log('⏰ 搜索超时，停止搜索');
							this.offDevicesDiscovery();
						}, timeoutMs);
						console.log(`✅ 设置搜索超时时间: ${timeoutMs}ms`);
					},
					fail: (err) => {
						console.error('开始搜索蓝牙设备失败:', err);
						if (err.errMsg && err.errMsg.includes('already discovering')) {
							console.log('设备搜索已在进行中，尝试重置...');
							// 如果还是提示已在搜索，再次尝试停止并重新开始
							this.offDevicesDiscovery();
							setTimeout(() => {
								this.startBluetoothDiscovery();
							}, 500);
						} else {
							uni.showToast({
								title: '搜索失败，请重试',
								icon: 'none'
							});
						}
					}
				});
			}, 200);
		},

		// 下拉刷新
		handlePullDownRefresh() {
			console.log('🔄 用户触发下拉刷新');
			this.refreshing = true;
			setTimeout(() => {
				this.refreshing = false;
				console.log('🔄 下拉刷新完成');
			}, 1500);

			// 先停止当前搜索，再重新开始
			console.log('🔄 停止当前搜索，准备重新开始');
			this.offDevicesDiscovery();
			setTimeout(() => {
				console.log('🔄 重新开始搜索设备');
				this.onDevicesDiscovery();
			}, 100);
		},

		// 刷新恢复
		onRefreshRestore() {
			this.refreshing = false;
		},

		// 蓝牙适配器状态改变
		handleBluetoothAdapterStateChange(res) {
			// #ifdef MP-WEIXIN
			console.log('📡 蓝牙适配器状态改变:', res);
			uni.hideLoading();
			// 修复：使用正确的数据结构
			const available = res.adapterState ? res.adapterState.available : res.available;
			const originState = wx.getStorageSync('bluetoothAdapterState');

			// 更新页面显示的蓝牙状态
			this.bluetoothState = available;
			wx.setStorageSync('bluetoothAdapterState', available);
			console.log('📡 蓝牙状态改变，页面状态已更新为:', this.bluetoothState);

			if (!available) {
				console.log('📡 蓝牙不可用，停止搜索');
				this.offDevicesDiscovery();
				wx.showLoading({ title: "请打开手机蓝牙", mask: true });
			} else if (!originState) {
				console.log('📡 蓝牙重新可用，开始搜索');
				this.onDevicesDiscovery();
			} else {
				console.log('📡 蓝牙状态无变化，继续当前操作');
			}
			// #endif
		},

		// BLE蓝牙连接状态改变
		async handleBLEConnectionStateChange(res) {
			// #ifdef MP-WEIXIN
			const { connected, deviceId } = res;
			if (connected) {
				await wx.showToast({ title: '连接成功', icon: 'success' });
				wx.setStorageSync('connectedDeviceId', deviceId);
			} else {
				await wx.showToast({ title: '已断开连接', icon: 'none' });
				wx.removeStorageSync('connectedDeviceId');
			}
			// #endif
		},

		// 过滤设备 - 按照原来的逻辑
		filterDevices(devices) {
			return devices.filter(d => {
				// 检查是否为信标设备（使用原始逻辑）
				this.checkBeacon(d) && (d.beacon = true);
				// 按照原来的逻辑：只显示有 name 和 localName 的设备
				return d.name && d.localName;
			});
		},

		// 检查是否为beacon设备（原始逻辑）
		checkBeacon(device) {
			const { advertisData } = device;
			if (!advertisData) return false;
			const uint8Array = new Uint8Array(advertisData);
			return uint8Array[2] === 0x02 && uint8Array[3] === 0x15;
		},

		// 搜索附近设备回调 - 按照原来的逻辑
		handleFoundBluetoothDevices({ devices }) {
			console.log('发现设备:', devices);
			const devicesList = [...this.devicesList];
			const filteredDevices = this.filterDevices(devices);
			console.log('过滤后的设备:', filteredDevices);

			for (let device of filteredDevices) {
				// 添加防护性检查
				if (!device || !device.deviceId) {
					console.warn('跳过无效设备:', device);
					continue;
				}

				const { deviceId } = device;
				const index = this._deviceIdsList.indexOf(deviceId);
				if (index < 0) {
					// 新设备
					this._deviceIdsList.push(deviceId);
					devicesList.push(device);
					console.log('添加新设备:', device.name || device.localName, deviceId);
				} else {
					// 更新现有设备
					devicesList.splice(index, 1, device);
					console.log('更新设备:', device.name || device.localName, deviceId);
				}
			}

			this.devicesList = devicesList;
			console.log('当前设备列表:', this.devicesList);
		},

		// 关闭搜索附近设备
		offDevicesDiscovery() {
			console.log('🛑 停止蓝牙设备搜索');
			if (this._discoveryTimer) {
				clearTimeout(this._discoveryTimer);
				this._discoveryTimer = null;
				console.log('🛑 已清除搜索超时定时器');
			}
			// #ifdef MP-WEIXIN
			try {
				wx.offBluetoothDeviceFound();
				console.log('🛑 已取消设备发现监听');
			} catch (e) {
				console.log('🛑 取消设备发现监听时出错:', e);
			}

			try {
				wx.stopBluetoothDevicesDiscovery({
					success: () => {
						console.log('🛑 已停止蓝牙设备搜索');
					},
					fail: (err) => {
						console.log('🛑 停止蓝牙设备搜索失败:', err);
						// 如果失败，可能是因为没有在搜索，这是正常的
					}
				});
			} catch (e) {
				console.log('🛑 停止蓝牙设备搜索时出错:', e);
			}
			// #endif
		},

		// 点击设备配对连接 - 按照原来的逻辑
		async onTapDevice(device) {
			// 添加防护性检查
			if (!device) {
				console.error('onTapDevice: device 参数为空');
				return;
			}

			const { deviceId, name: localName, beacon, connectable } = device;

			// 检查 deviceId 是否存在
			if (!deviceId) {
				console.error('onTapDevice: deviceId 为空');
				return;
			}

			// 按照原来的逻辑：检查 beacon、connectable 和蓝牙状态
			if (beacon || !connectable || !wx.getStorageSync('bluetoothAdapterState')) {
				return;
			}

			// #ifdef MP-WEIXIN
			try {
				wx.showLoading({ title: '正在连接...' });

				// 创建BLE连接
				await wx.createBLEConnection({
					deviceId,
					timeout: 10000
				});

				// 连接成功后，读取服务列表
				await wx.getBLEDeviceServices({ deviceId });

				// HC系列蓝牙设备直接进入串口收发页面
				// 由于暂时没有引入配置常量，先跳过这部分逻辑

				// 非HC系列蓝牙设备进入UUID选择页面
				wx.hideLoading();
				const query = `deviceId=${deviceId}&deviceName=${encodeURIComponent(localName || 'Unknown')}`;
				wx.navigateTo({
					url: `/pages/uuidList/index?${query}`
				});

			} catch (err) {
				wx.hideLoading();
				console.error('连接设备失败:', err);
				uni.showToast({
					title: '连接失败，请重试',
					icon: 'none'
				});
			}
			// #endif
		}
	}
}
</script>

<style>
/* 页面样式 */
.page {
	width: 100vw;
	height: 100vh;
	background-color: #f5f5f5;
}

/* 头部样式 */
.deviceList-header {
	padding: 0 40rpx;
	height: 80rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	background-color: #fff;
	font-size: 30rpx;
}

/* 内容区域样式 */
.deviceList-content {
	width: 100vw;
	height: calc(100vh - 80rpx);
	padding: 20rpx;
	box-sizing: border-box;
}

/* 设备卡片样式 */
.deviceCard-wrap {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 40rpx;
	width: 100%;
	height: 180rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-sizing: border-box;
}

/* 设备名称样式 */
.device-name {
	display: flex;
	align-items: center;
	font-size: 30rpx;
	font-weight: bold;
}

/* 信标标签样式 */
.device-name .beacon {
	margin-left: 15rpx;
	padding: 3rpx 8rpx;
	background-color: yellowgreen;
	border-radius: 5rpx;
	font-size: 20rpx;
	color: #fff;
}

/* 设备ID样式 */
.device-id {
	font-size: 23rpx;
	font-weight: light;
	line-height: 35rpx;
	color: #999;
}

/* RSSI信号强度样式 */
.device-rssi {
	font-size: 25rpx;
}

/* 调试信息样式 */
.debug-info {
	border-radius: 8rpx;
	line-height: 1.5;
}

/* 空状态提示样式 */
.empty-tip {
	text-align: center;
	padding: 60rpx 20rpx;
	color: #999;
	font-size: 28rpx;
	line-height: 1.5;
}
</style>
