page {
  padding: 15rpx !important;
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.nav-wrap {
  width: 100vw;
  --td-navbar-color: #fff;
  --td-navbar-bg-color: #0052d9;
}

.nav-header {
  margin: 30rpx 0;
  width: 100%;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  box-sizing: border-box;
  font-size: 30rpx;
}

.uuidList-item-wrap {
  background-color: #fff;
  border-radius: 8rpx;
  min-height: 150rpx !important;
  height: auto;
  font-size: 30rpx !important;
  font-weight: lighter !important;
}

.uuidCard-header {
  width: 100%;
  height: 150rpx !important;
  box-sizing: border-box;
}

.uuidCard-header .header-label {
  display: flex;
  align-items: center;
}

.uuidCard-header .header-label .bolder {
  font-size: 25rpx;
  color: #333;
  font-weight: bolder;
}

.uuidCard-header .header-sub-label {
  font-size: 20rpx;
  color: #bbb;
}

.uuidCard-header .header-content {
  color: #4287ff;
  font-size: 25rpx;
  font-weight: lighter;
}

.uuidCard-content {
  padding: 25rpx 0;
}

.uuidCard-content .sub-header {
  width: 100%;
  height: 35rpx;
  color: #333;
  font-size: 25rpx;
  font-weight: bolder;
}

.uuidCard-content .sub-header .bold {
  font-weight: bold;
}

.sub-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
}

.sub-content:last-child {
  border: none;
}

.sub-content-left {
  font-size: 25rpx;
  font-weight: lighter;
  color: #4287ff;
}

.sub-content-right {
  font-size: 20rpx;
  font-weight: lighter;
  color: #999
}