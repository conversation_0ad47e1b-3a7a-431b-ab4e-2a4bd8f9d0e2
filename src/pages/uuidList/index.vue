<template>
	<view class="container">
		<view class="nav-wrap">
			<text class="nav-title">{{ deviceName }}</text>
		</view>

		<view class="nav-header">
			<text>服务列表</text>
		</view>

		<view class="uuidList-body">
			<view
				v-for="(service, index) in deviceServices"
				:key="service.uuid"
				class="uuidList-item-wrap"
			>
				<view class="uuidCard-header" @click="toggleService(index)">
					<view class="header-label">
						<text class="bolder">ServiceUUID</text>
						<text v-if="service.isPrimary" class="header-sub-label">&nbsp;Primary</text>
					</view>
					<view class="header-content">{{ service.uuid }}</view>
				</view>

				<view v-if="service.expanded && service.characteristics.length" class="uuidCard-content">
					<view class="sub-header">
						<text class="bold">Characteristics</text>
					</view>
					<view
						v-for="characteristic in service.characteristics"
						:key="characteristic.uuid"
						class="sub-content"
						@click="handleCharacteristicTap(service.uuid, characteristic)"
					>
						<view class="sub-content-header">
							<text class="bold">CharacteristicUUID</text>
						</view>
						<view class="sub-content-body">{{ characteristic.uuid }}</view>
						<view class="sub-content-footer">
							<view class="properties-wrap">
								<template v-for="(value, key) in characteristic.properties">
									<text
										v-if="value"
										:key="key"
										class="property-tag"
									>
										{{ key }}
									</text>
								</template>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
// 按照原来的逻辑引入依赖
// import { transToQuery, handleErrno } from '@utils/index'

// 简化版本的工具函数
function transToQuery(obj) {
	const params = []
	for (const key in obj) {
		if (obj.hasOwnProperty(key) && obj[key] !== undefined && obj[key] !== null) {
			params.push(`${key}=${encodeURIComponent(obj[key])}`)
		}
	}
	return params.join('&')
}

export default {
	data() {
		return {
			deviceId: '',
			deviceName: '设备名称',
			deviceServices: []
		}
	},

	// 按照原来的逻辑实现 onLoad
	async onLoad(options) {
		const { deviceId, deviceName } = options
		try {
			const { services } = await wx.getBLEDeviceServices({ deviceId })
			for (let service of services) {
				const suuid = service.uuid
				const { characteristics } = await wx.getBLEDeviceCharacteristics({ deviceId, serviceId: suuid })
				service['characteristics'] = characteristics
				service['expanded'] = false // 添加展开状态
			}
			this.deviceId = deviceId
			this.deviceName = deviceName
			this.deviceServices = services
			// 设置导航栏标题
			wx.setNavigationBarTitle({ title: deviceName })
		} catch (err) {
			if (err.errno) {
				// handleErrno(err.errno)
				console.error('错误码:', err.errno)
			} else {
				console.error(err)
			}
		}
	},

	onUnload() {
		wx.closeBLEConnection({ deviceId: this.deviceId })
	},

	methods: {


		// 切换服务展开状态
		toggleService(index) {
			this.deviceServices[index].expanded = !this.deviceServices[index].expanded
		},

		// 按照原来的逻辑处理特征点击
		handleCharacteristicTap(serviceId, characteristic) {
			const { deviceId, deviceName } = this
			const query = transToQuery({
				origin: 'uuidList',
				deviceId,
				deviceName,
				serviceId,
				characteristicId: characteristic.uuid,
				...characteristic.properties
			})
			wx.navigateTo({
				url: `/pages/comm/index?${query}`
			})
		}
	}
}
</script>

<style>
page {
	padding: 15rpx !important;
	width: 100vw;
	height: 100vh;
	background-color: #f5f5f5;
	box-sizing: border-box;
}

.container {
	width: 100%;
	height: 100%;
}

.nav-wrap {
	width: 100vw;
	background-color: #0052d9;
	color: #fff;
	padding: 20rpx;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.nav-title {
	color: #fff;
}

.nav-header {
	margin: 30rpx 0;
	width: 100%;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	box-sizing: border-box;
	font-size: 30rpx;
}

.uuidList-item-wrap {
	background-color: #fff;
	border-radius: 8rpx;
	min-height: 150rpx !important;
	height: auto;
	font-size: 30rpx !important;
	font-weight: lighter !important;
	margin-bottom: 20rpx;
}

.uuidCard-header {
	width: 100%;
	height: 150rpx !important;
	box-sizing: border-box;
	padding: 20rpx;
	background-color: #0052d9;
	color: #fff;
	border-radius: 8rpx 8rpx 0 0;
}

.header-label {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.bolder {
	font-weight: bold;
	font-size: 28rpx;
}

.header-sub-label {
	font-size: 24rpx;
	color: #ccc;
}

.header-content {
	font-size: 26rpx;
	word-break: break-all;
}

.uuidCard-content {
	padding: 20rpx;
}

.sub-header {
	margin-bottom: 15rpx;
}

.bold {
	font-weight: bold;
	font-size: 26rpx;
	color: #333;
}

.sub-content {
	background-color: #f8f9fa;
	border-radius: 8rpx;
	padding: 15rpx;
	margin-bottom: 15rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.sub-content:active {
	border-color: #0052d9;
	background-color: #e3f2fd;
}

.sub-content-header {
	margin-bottom: 8rpx;
}

.sub-content-body {
	font-size: 24rpx;
	color: #666;
	word-break: break-all;
	margin-bottom: 10rpx;
}

.sub-content-footer {
	display: flex;
	justify-content: flex-end;
}

.properties-wrap {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.property-tag {
	background-color: #0052d9;
	color: #fff;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
}
</style>
