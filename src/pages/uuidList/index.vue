<template>
	<view class="container">
		<view class="header">
			<text class="title">服务特征列表</text>
			<text class="device-name">{{ deviceName }}</text>
		</view>

		<view class="service-list">
			<view v-if="servicesList.length === 0" class="empty-tip">
				<text>正在获取设备服务...</text>
			</view>
			
			<view 
				v-for="(service, index) in servicesList" 
				:key="service.uuid" 
				class="service-card"
			>
				<view class="service-header" @click="toggleService(index)">
					<text class="service-uuid">{{ service.uuid }}</text>
					<text class="service-toggle">{{ service.expanded ? '▼' : '▶' }}</text>
				</view>
				
				<view v-if="service.expanded" class="characteristics-list">
					<view 
						v-for="characteristic in service.characteristics" 
						:key="characteristic.uuid"
						class="characteristic-card"
						@click="onTapCharacteristic(service.uuid, characteristic)"
					>
						<view class="characteristic-header">
							<text class="characteristic-uuid">{{ characteristic.uuid }}</text>
						</view>
						<view class="characteristic-properties">
							<text 
								v-for="property in characteristic.properties" 
								:key="property"
								class="property-tag"
								:class="getPropertyClass(property)"
							>
								{{ property }}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			deviceId: '',
			deviceName: '',
			servicesList: []
		}
	},
	
	onLoad(options) {
		this.deviceId = options.deviceId || '';
		this.deviceName = decodeURIComponent(options.deviceName || '未知设备');
		
		if (this.deviceId) {
			this.getServices();
		} else {
			uni.showModal({
				title: '错误',
				content: '设备ID不能为空',
				showCancel: false,
				success: () => {
					uni.navigateBack();
				}
			});
		}
	},
	
	onUnload() {
		// 断开蓝牙连接
		this.disconnectDevice();
	},
	
	methods: {
		// 获取设备服务
		getServices() {
			uni.showLoading({
				title: '获取服务中...'
			});
			
			// #ifdef MP-WEIXIN
			wx.getBLEDeviceServices({
				deviceId: this.deviceId,
				success: (res) => {
					console.log('获取到的服务:', res.services);
					this.processServices(res.services);
					uni.hideLoading();
				},
				fail: (err) => {
					console.error('获取服务失败:', err);
					uni.hideLoading();
					uni.showModal({
						title: '获取服务失败',
						content: '请确保设备已正确连接',
						showCancel: false,
						success: () => {
							uni.navigateBack();
						}
					});
				}
			});
			// #endif
		},
		
		// 处理服务数据
		processServices(services) {
			const servicesList = services.map(service => ({
				uuid: service.uuid,
				isPrimary: service.isPrimary,
				expanded: false,
				characteristics: []
			}));
			
			this.servicesList = servicesList;
			
			// 获取每个服务的特征
			servicesList.forEach((service, index) => {
				this.getCharacteristics(service.uuid, index);
			});
		},
		
		// 获取特征
		getCharacteristics(serviceId, serviceIndex) {
			// #ifdef MP-WEIXIN
			wx.getBLEDeviceCharacteristics({
				deviceId: this.deviceId,
				serviceId: serviceId,
				success: (res) => {
					console.log(`服务 ${serviceId} 的特征:`, res.characteristics);
					this.servicesList[serviceIndex].characteristics = res.characteristics;
				},
				fail: (err) => {
					console.error(`获取服务 ${serviceId} 特征失败:`, err);
				}
			});
			// #endif
		},
		
		// 切换服务展开/收起
		toggleService(index) {
			this.servicesList[index].expanded = !this.servicesList[index].expanded;
		},
		
		// 点击特征
		onTapCharacteristic(serviceId, characteristic) {
			console.log('选择特征:', serviceId, characteristic);
			
			// 检查特征是否支持读写
			const canRead = characteristic.properties.read;
			const canWrite = characteristic.properties.write || characteristic.properties.writeNoResponse;
			const canNotify = characteristic.properties.notify || characteristic.properties.indicate;
			
			if (!canRead && !canWrite && !canNotify) {
				uni.showToast({
					title: '该特征不支持读写操作',
					icon: 'none'
				});
				return;
			}
			
			// 跳转到通信页面
			const query = [
				`deviceId=${this.deviceId}`,
				`deviceName=${encodeURIComponent(this.deviceName)}`,
				`serviceId=${serviceId}`,
				`characteristicId=${characteristic.uuid}`,
				`canRead=${canRead}`,
				`canWrite=${canWrite}`,
				`canNotify=${canNotify}`
			].join('&');
			
			uni.navigateTo({
				url: `/pages/comm/index?${query}`
			});
		},
		
		// 获取属性样式类
		getPropertyClass(property) {
			const classMap = {
				'read': 'property-read',
				'write': 'property-write',
				'writeNoResponse': 'property-write',
				'notify': 'property-notify',
				'indicate': 'property-notify'
			};
			return classMap[property] || 'property-default';
		},
		
		// 断开设备连接
		disconnectDevice() {
			// #ifdef MP-WEIXIN
			if (this.deviceId) {
				wx.closeBLEConnection({
					deviceId: this.deviceId,
					success: () => {
						console.log('蓝牙连接已断开');
					},
					fail: (err) => {
						console.error('断开连接失败:', err);
					}
				});
			}
			// #endif
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.device-name {
	font-size: 28rpx;
	color: #666;
}

.service-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.empty-tip {
	text-align: center;
	padding: 60rpx 0;
	color: #999;
	font-size: 28rpx;
}

.service-card {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.service-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background-color: #1976D2;
	color: #fff;
}

.service-uuid {
	font-size: 28rpx;
	font-weight: bold;
	flex: 1;
}

.service-toggle {
	font-size: 24rpx;
	margin-left: 20rpx;
}

.characteristics-list {
	padding: 20rpx;
}

.characteristic-card {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.characteristic-card:active {
	border-color: #1976D2;
	background-color: #e3f2fd;
}

.characteristic-header {
	margin-bottom: 15rpx;
}

.characteristic-uuid {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.characteristic-properties {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.property-tag {
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	color: #fff;
}

.property-read {
	background-color: #4caf50;
}

.property-write {
	background-color: #ff9800;
}

.property-notify {
	background-color: #2196f3;
}

.property-default {
	background-color: #9e9e9e;
}
</style>
