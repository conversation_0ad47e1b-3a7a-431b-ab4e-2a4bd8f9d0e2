<template>
	<view class="login-container">

		<view class="login-form">
			<view class="header">
				<text class="app-name">安电终端</text>
			</view>
			<view class="input-group">
				<text class="input-label">用户名</text>
				<input class="input-box" type="text" v-model="username" placeholder="请输入用户名" />
			</view>

			<view class="input-group">
				<text class="input-label">密码</text>
				<view class="password-container">
					<input class="input-box" type="password" v-model="password" placeholder="请输入密码" />
				</view>
			</view>

			<button class="login-btn" @tap="handleLogin">登 录</button>
		</view>
	</view>
</template>

<script>
	import { getUserRoleTypes, hasRole, RoleType } from '@/utils/roles';

	// 消息订阅模板ID
	const SUBSCRIBE_TEMPLATE_ID = 'NvSnWo3_QukkvtCZYnP7LUJkCogj_Ylr8IaCVJHP-1s';

	export default {
		data() {
			return {
				username: '',
				password: '',
				rememberPassword: false,
				subscribeTemplateId: SUBSCRIBE_TEMPLATE_ID // 订阅消息模板ID
			}
		},
		onLoad() {
			// 检查是否有保存的登录信息
			const savedUsername = uni.getStorageSync('username');
			const savedPassword = uni.getStorageSync('password');

			if (savedUsername && savedPassword) {
				this.username = savedUsername;
				this.password = savedPassword;
				this.rememberPassword = true;
			}
		},
		methods: {
			toggleRemember() {
				this.rememberPassword = !this.rememberPassword;
			},

			// 获取个人信息
			getUserInfo(token) {
				// 显示加载中
				uni.showLoading({
					title: '获取个人信息...'
				});

				// 调用/me API获取个人信息
				this.$http.get('/me', {}, {
					header: {
						'xaspsession': token
					}
				})
					.then(data => {
						uni.hideLoading();

						// 打印完整的用户信息，用于调试
						console.log('获取到的完整用户信息:', data);

						// 保存个人信息到本地
						uni.setStorageSync('userInfo', data);

						// 单独保存关键字段，方便在其他地方访问
						uni.setStorageSync('userAdmin', data.admin === true);
						uni.setStorageSync('userGroupAdmin', data.groupAdmin === true);
						uni.setStorageSync('userGroupId', data.groupId);
						uni.setStorageSync('userGroupName', data.groupName);
						uni.setStorageSync('userId', data.id);
						uni.setStorageSync('userName', data.name);
						uni.setStorageSync('userRealName', data.realName);
						uni.setStorageSync('openId', data.openId);

						// 保存用户角色信息
						const roleIdList = data.roleIdList || [];
						const userRoleTypes = getUserRoleTypes(roleIdList);
						uni.setStorageSync('userRoleIdList', roleIdList);
						uni.setStorageSync('userRoleTypes', userRoleTypes);

						// 检查用户是否具有特定角色
						const isAdmin = hasRole(roleIdList, RoleType.ADMIN);
						const isOperator = hasRole(roleIdList, RoleType.OPERATOR);
						const isGroupAdmin = hasRole(roleIdList, RoleType.GROUP_ADMIN);
						const isGroupOperator = hasRole(roleIdList, RoleType.GROUP_OPERATOR);
						const isInspector = hasRole(roleIdList, RoleType.INSPECTOR);
						const isVisitor = hasRole(roleIdList, RoleType.VISITOR);

						// 保存用户角色标志
						uni.setStorageSync('isAdmin', isAdmin);
						uni.setStorageSync('isOperator', isOperator);
						uni.setStorageSync('isGroupAdmin', isGroupAdmin);
						uni.setStorageSync('isGroupOperator', isGroupOperator);
						uni.setStorageSync('isInspector', isInspector);
						uni.setStorageSync('isVisitor', isVisitor);

						// 检查是否已经获取到用户的openId
						if (!data.openId) {
							console.log('用户没有openId，尝试获取...');
							this.getWxOpenId(token);
						} else {
							console.log('用户已有openId:', data.openId);

							// 先显示登录成功提示
							uni.showToast({
								title: '登录成功',
								icon: 'success',
								duration: 1500
							});

							// 跳转到点检页面
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					})
					.catch(err => {
						uni.hideLoading();
						console.error('获取个人信息请求失败:', err);
				});
			},

			// 刷新用户信息，获取更新后的openId
			refreshUserInfo(token) {
				console.log('刷新用户信息...');

				// 调用/me API获取最新的个人信息
				this.$http.get('/me', {}, {
					header: {
						'xaspsession': token
					}
				})
				.then(data => {
					console.log('获取到更新后的用户信息:', data);

					// 更新openId
					if (data && data.openId) {
						uni.setStorageSync('openId', data.openId);
						console.log('成功获取并保存openId:', data.openId);
					} else {
						console.log('用户信息中仍然没有openId');
					}
				})
				.catch(err => {
					console.error('刷新用户信息失败:', err);
				});
			},

			// 获取微信用户的openId并更新到后端
			getWxOpenId(token) {
				console.log('开始获取微信openId...');

				// 调用微信登录接口获取code
				wx.login({
					success: (res) => {
						if (res.code) {
							console.log('微信登录成功，获取到code:', res.code);

							// 将code发送到后端，绑定微信账号
							this.$http.post('/wechat/bind', {
								code: res.code
							}, {
								header: {
									'xaspsession': token
								}
							})
							.then(success => {
								console.log('绑定微信结果:', success);

								if (success) {
									console.log('绑定微信成功，获取用户信息...');
									// 重新获取用户信息，以获取更新后的openId
									this.refreshUserInfo(token);
								} else {
									console.log('绑定微信失败');
								}

								// 显示登录成功提示
								uni.showToast({
									title: '登录成功',
									icon: 'success',
									duration: 1500,
									success: () => {
										// 跳转到点检页面
										setTimeout(() => {
											uni.switchTab({
												url: '/pages/index/index'
											});
										}, 1500);
									}
								});
							})
							.catch(err => {
								console.error('绑定微信失败:', err);

								// 即使绑定失败，也允许用户继续使用
								uni.showToast({
									title: '登录成功，但微信绑定失败',
									icon: 'none',
									duration: 1500,
									success: () => {
										setTimeout(() => {
											uni.switchTab({
												url: '/pages/index/index'
											});
										}, 1500);
									}
								});
							});
						} else {
							console.error('微信登录失败:', res.errMsg);

							// 即使获取code失败，也允许用户继续使用
							uni.showToast({
								title: '登录成功，但微信授权失败',
								icon: 'none',
								duration: 1500,
								success: () => {
									setTimeout(() => {
										uni.switchTab({
											url: '/pages/index/index'
										});
									}, 1500);
								}
							});
						}
					},
					fail: (err) => {
						console.error('调用微信登录接口失败:', err);

						// 即使wx.login失败，也允许用户继续使用
						uni.showToast({
							title: '登录成功，但微信登录失败',
							icon: 'none',
							duration: 1500,
							success: () => {
								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index'
									});
								}, 1500);
							}
						});
					}
				});
			},

			handleLogin() {
				// 验证输入
				if (!this.username.trim()) {
					uni.showToast({
						title: '请输入用户名',
						icon: 'none'
					});
					return;
				}

				if (!this.password.trim()) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '登录中...'
				});

				// 实际登录请求
				this.$http.post('/token', {
					username: this.username,
					password: this.password
				})
					.then(data => {
						uni.hideLoading();

						// 检查响应状态
						if (data && data.token) {
							// 保存token和登录状态
							uni.setStorageSync('token', data.token);
							uni.setStorageSync('tokenExpireAt', data.expAt);
							uni.setStorageSync('isLoggedIn', true);

							// 如果选择了记住密码，则保存用户名和密码
							if (this.rememberPassword) {
								uni.setStorageSync('username', this.username);
								uni.setStorageSync('password', this.password);
							} else {
								uni.removeStorageSync('username');
								uni.removeStorageSync('password');
							}

							// 获取个人信息
							this.getUserInfo(data.token);
						} else {
							// 登录失败
							uni.showToast({
								title: '登录失败，请检查用户名和密码',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						uni.hideLoading();
						console.error('登录失败，请检查用户名和密码:', err);
						uni.showToast({
							title: '登录失败，请检查用户名和密码',
							icon: 'none'
						});
				});
			},

			// 调试用：模拟管理员登录
			debugLoginAsAdmin() {
				console.log('调试: 模拟管理员登录');

				// 保存登录状态和用户信息
				uni.setStorageSync('isLoggedIn', true);
				uni.setStorageSync('token', 'mock-token-' + Date.now());

				// 设置用户角色信息
				uni.setStorageSync('isAdmin', true);
				uni.setStorageSync('isGroupAdmin', true); // 添加组管理员角色
				uni.setStorageSync('isInspector', false);

				// 保存用户信息
				uni.setStorageSync('userInfo', {
					username: 'admin',
					role: 'admin',
					groupId: '1', // 模拟组ID
					admin: true
				});

				// 登录成功提示
				uni.showToast({
					title: '管理员登录成功',
					icon: 'success',
					duration: 1500,
					success: () => {
						// 跳转到点检页面
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 1500);
					}
				});
			},

			// 调试用：模拟点检员登录
			debugLoginAsInspector() {
				console.log('调试: 模拟点检员登录');

				// 保存登录状态和用户信息
				uni.setStorageSync('isLoggedIn', true);
				uni.setStorageSync('token', 'mock-token-' + Date.now());

				// 设置用户角色信息
				uni.setStorageSync('isAdmin', false);
				uni.setStorageSync('isGroupAdmin', false); // 确保不是组管理员
				uni.setStorageSync('isInspector', true);

				// 保存用户信息
				uni.setStorageSync('userInfo', {
					username: 'inspector',
					role: 'inspector',
					groupId: '1', // 模拟组ID
					admin: false
				});

				// 登录成功提示
				uni.showToast({
					title: '点检员登录成功',
					icon: 'success',
					duration: 1500,
					success: () => {
						// 跳转到点检页面
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 1500);
					}
				});
			}
		}
	}
</script>

<style>
	.login-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		min-height: 100vh;
		padding: 60rpx 40rpx;
		background-color: #fff;
	}

	.header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 60rpx;
	}

	.app-name {
		font-size: 48rpx;
		font-weight: bold;
		color: #1976D2;
		margin-bottom: 40rpx;
	}

	.login-form {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.input-group {
		margin-bottom: 40rpx;
	}

	.input-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		display: block;
	}

	.input-box {
		width: 100%;
		height: 90rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #333;
	}

	.password-container {
		position: relative;
		width: 100%;
	}

	.password-toggle {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		font-size: 28rpx;
		color: #1976D2;
		z-index: 1;
	}

	.remember-box {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.remember-text {
		font-size: 28rpx;
		color: #666;
		margin-left: 10rpx;
	}

	.login-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		background-color: #1976D2;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}

	.footer {
		margin-top: 60rpx;
	}

	.copyright {
		font-size: 24rpx;
		color: #999;
	}

	/* 调试按钮样式 */
	.debug-buttons {
		margin-top: 40rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.debug-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #f0f0f0;
		color: #666;
		font-size: 28rpx;
		border-radius: 10rpx;
	}
</style>
