/**
 * 蓝牙配置常量
 */

// 通用蓝牙服务UUID
export const GeneralServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB'

// 通用蓝牙特征值UUID
export const GeneralCharacteristicUUID = '0000FFE1-0000-1000-8000-00805F9B34FB'

// HC-02蓝牙模块服务UUID
export const HC02ServiceUUID = '0000FFE0-0000-1000-8000-00805F9B34FB'

// HC-02蓝牙模块写特征值UUID
export const HC02CharacteristicWriteUUID = '0000FFE1-0000-1000-8000-00805F9B34FB'

// HC-02蓝牙模块通知特征值UUID
export const HC02CharacteristicNotifyUUID = '0000FFE1-0000-1000-8000-00805F9B34FB'

// 其他蓝牙配置
export const BLUETOOTH_CONFIG = {
  // 搜索设备超时时间（毫秒）
  DISCOVERY_TIMEOUT: 30000,
  
  // 连接超时时间（毫秒）
  CONNECTION_TIMEOUT: 10000,
  
  // 数据包最大长度
  MAX_PACKAGE_SIZE: 20,
  
  // 搜索间隔
  DISCOVERY_INTERVAL: 1000
}
