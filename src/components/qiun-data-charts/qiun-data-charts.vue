<template>
  <view class="chartsview">
    <canvas v-if="canvasId" class="charts" :id="canvasId" :canvas-id="canvasId"
      :style="{width: cWidth + 'px', height: cHeight + 'px'}"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
      @tap="tap"></canvas>
    <view v-if="!isShowCharts" class="charts-empty">
      <view class="charts-empty-text">{{emptyText}}</view>
    </view>
  </view>
</template>

<script>
// 引入uCharts库
import uCharts from '@qiun/ucharts/u-charts.js';

export default {
  name: 'qiun-data-charts',
  props: {
    type: {
      type: String,
      default: 'column'
    },
    canvasId: {
      type: String,
      default: 'uchartsid'
    },
    chartData: {
      type: Object,
      default: () => ({
        categories: [],
        series: []
      })
    },
    width: {
      type: Number,
      default: 750
    },
    height: {
      type: Number,
      default: 500
    },
    pixelRatio: {
      type: Number,
      default: 1
    },
    background: {
      type: String,
      default: '#FFFFFF'
    },
    animation: {
      type: Boolean,
      default: true
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    enableScroll: {
      type: Boolean,
      default: false
    },
    extra: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      cWidth: 375,
      cHeight: 250,
      isShowCharts: true,
      uchartsOpts: {},
      uchartsInstance: null
    };
  },
  watch: {
    chartData: {
      handler(val) {
        if (val && val.series && val.series.length > 0) {
          this.isShowCharts = true;
          this.drawCharts();
        } else {
          this.isShowCharts = false;
        }
      },
      deep: true
    }
  },
  mounted() {
    this.cWidth = uni.upx2px(this.width);
    this.cHeight = uni.upx2px(this.height);

    // 初始化图表
    if (this.chartData && this.chartData.series && this.chartData.series.length > 0) {
      this.isShowCharts = true;
      this.$nextTick(() => {
        this.drawCharts();
      });
    } else {
      this.isShowCharts = false;
    }
  },
  methods: {
    drawCharts() {
      const ctx = uni.createCanvasContext(this.canvasId, this);

      // 配置项
      const opts = {
        type: this.type,
        context: ctx,
        width: this.cWidth,
        height: this.cHeight,
        pixelRatio: this.pixelRatio,
        animation: this.animation,
        background: this.background,
        categories: this.chartData.categories || [],
        series: this.chartData.series || [],
        enableScroll: this.enableScroll,
        legend: {
          show: true,
          position: 'bottom',
          float: 'center',
          padding: 10
        },
        xAxis: {
          disableGrid: false, // 启用网格线
          itemCount: 4, // 设置为类别数量
          labelCount: 4, // 设置为类别数量
          rotateLabel: false, // 禁用标签旋转
          disabled: false, // 确保X轴不被禁用
          autoSkipLabel: false, // 禁用自动跳过标签
          axisLine: true, // 显示坐标轴线
          boundaryGap: true, // 设置边界间隙
          calibration: true, // 显示刻度线
          labelWidth: 80, // 增加标签宽度
          interval: 0, // 强制显示所有标签
          format: (val) => {
            // 直接返回原始值，不做任何处理
            return val;
          }
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 4,
          data: [
            {
              min: 0
            }
          ]
        },
        extra: this.extra
      };

      // 确保从extra中合并xAxis配置
      if (this.extra && this.extra.xAxis) {
        opts.xAxis = Object.assign({}, opts.xAxis, this.extra.xAxis);
      }

      // 特殊处理：确保所有X轴标签都能显示
      if (this.chartData && this.chartData.categories) {
        // 设置labelCount和itemCount与类别数量一致
        const categoryCount = this.chartData.categories.length;
        console.log('图表类别数量:', categoryCount);
        if (categoryCount > 0) {
          opts.xAxis.labelCount = categoryCount;
          opts.xAxis.itemCount = categoryCount;

          // 根据类别数量调整X轴配置
          opts.xAxis.disabled = false;
          opts.xAxis.autoSkipLabel = false;
          opts.xAxis.boundaryGap = true;

          // 禁用标签旋转，增加标签宽度
          opts.xAxis.rotateLabel = false;
          opts.xAxis.labelWidth = 60;

          // 增加底部内边距，为标签留出足够空间
          if (opts.padding && opts.padding.length >= 4) {
            opts.padding[2] = 60; // 底部内边距
          } else {
            opts.padding = [15, 15, 60, 15];
          }

          // 调整柱状图宽度，确保标签不重叠
          if (opts.column) {
            // 根据类别数量动态调整柱子宽度和间距
            if (categoryCount <= 2) {
              opts.column.width = 30;
              opts.column.categoryGap = 30;
            } else if (categoryCount <= 4) {
              opts.column.width = 20;
              opts.column.categoryGap = 20;
            } else {
              opts.column.width = Math.min(15, 60 / categoryCount);
              opts.column.categoryGap = 10;
            }

            // 使用分组类型
            opts.column.type = 'group';

            // 减小系列间距
            opts.column.seriesGap = 0;

            // 确保X轴标签显示完整
            opts.xAxis.boundaryGap = true;
            opts.xAxis.axisLine = true;
            opts.xAxis.labelShow = true;

            // 禁用自动跳过标签
            opts.xAxis.autoSkipLabel = false;
          }

          // 不再修改类别名称，而是通过配置确保所有标签都能显示
          if (this.chartData && this.chartData.categories) {
            // 确保X轴配置能够显示所有标签
            opts.xAxis.boundaryGap = true;
            opts.xAxis.axisLine = true;
            opts.xAxis.labelShow = true;
            opts.xAxis.autoSkipLabel = false;
            opts.xAxis.rotateLabel = false;

            // 如果是柱状图，调整配置以确保标签显示
            if (this.type === 'column') {
              // 确保 opts.extra 存在
              if (!opts.extra) opts.extra = {};
              // 确保 opts.extra.column 存在
              if (!opts.extra.column) opts.extra.column = {};

              // 设置柱状图类型为分组
              opts.extra.column.type = 'group';

              // 根据类别数量调整柱子宽度
              const categoryCount = this.chartData.categories.length;
              if (categoryCount <= 2) {
                opts.extra.column.width = 30;
              } else if (categoryCount <= 4) {
                opts.extra.column.width = 20;
              } else {
                opts.extra.column.width = 15;
              }

              // 设置类别间距
              opts.extra.column.categoryGap = 20;
            }

            console.log('X轴配置已优化，确保所有标签显示');
          }

          console.log('已设置labelCount和itemCount为:', categoryCount);
          console.log('X轴完整配置:', opts.xAxis);
          console.log('柱状图配置:', opts.column);
        }
      }

      this.uchartsOpts = opts;

      // 特殊处理：确保柱状图颜色数组正确传递
      if (this.type === 'column' && this.chartData && this.chartData.series && this.chartData.series.length > 0) {
        // 检查是否有颜色数组
        const series = this.chartData.series[0];
        if (series && series.color && Array.isArray(series.color)) {
          console.log('检测到柱状图颜色数组:', series.color);

          // 直接修改 config.color 来设置全局颜色
          opts.color = series.color;

          // 确保 opts.extra 存在
          if (!opts.extra) opts.extra = {};
          // 确保 opts.extra.column 存在
          if (!opts.extra.column) opts.extra.column = {};
          // 设置柱状图颜色
          opts.extra.column.color = series.color;

          // 强制设置柱状图类型为分组
          opts.extra.column.type = 'group';

          console.log('已设置柱状图颜色数组:', opts.color);
          console.log('已设置柱状图类型为分组');
        }
      }

      // 创建图表实例
      this.uchartsInstance = new uCharts(opts);
      this.uchartsInstance.addEventListener('renderComplete', () => {
        this.$emit('complete', {
          type: 'complete',
          complete: true,
          id: this.canvasId
        });
      });
      this.uchartsInstance.addEventListener('scrollLeft', () => {
        this.$emit('scrollLeft', {
          type: 'scrollLeft',
          scrollLeft: true,
          id: this.canvasId
        });
      });
      this.uchartsInstance.addEventListener('scrollRight', () => {
        this.$emit('scrollRight', {
          type: 'scrollRight',
          scrollRight: true,
          id: this.canvasId
        });
      });
    },

    // 图表点击事件
    tap(e) {
      if (this.uchartsInstance) {
        const currentIndex = this.uchartsInstance.getCurrentDataIndex(e);
        if (currentIndex !== -1) {
          this.$emit('getIndex', {
            type: 'getIndex',
            index: currentIndex,
            id: this.canvasId
          });
        }
      }
    },

    // 图表触摸事件
    touchStart(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.touchLegend(e);
        this.uchartsInstance.scrollStart(e);
      }
    },
    touchMove(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.scroll(e);
      }
    },
    touchEnd(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.scrollEnd(e);
      }
    }
  }
};
</script>

<style>
.chartsview {
  width: 100%;
  height: 100%;
  position: relative;
}

.charts {
  width: 100% !important;
  height: 100% !important;
  animation: fadeIn 0.5s;
}

.charts-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.charts-empty-text {
  color: #999;
  font-size: 13px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
