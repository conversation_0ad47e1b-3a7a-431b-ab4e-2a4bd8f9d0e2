<template>
  <view class="ec-chart-container" :style="{ height: height + 'px' }">
    <ec-canvas
      v-if="canvasId"
      :canvasId="canvasId"
      :lazyLoad="false"
      :disableTouch="false"
      :throttleTouch="true"
      @init="onInit"
    ></ec-canvas>
  </view>
</template>

<script>
import EcCanvas from './ec-canvas/ec-canvas.vue';

export default {
  components: {
    EcCanvas
  },
  props: {
    chartType: {
      type: String,
      default: 'bar' // 可选值：bar, line, pie
    },
    chartData: {
      type: Array,
      default: () => []
    },
    chartOptions: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      chart: null,
      canvasId: `ec-canvas-${Date.now()}`
    };
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true
    },
    chartOptions: {
      handler(newVal) {
        this.updateChart();
      },
      deep: true
    }
  },
  methods: {
    onInit(e) {
      console.log('Chart initialized:', e);
      this.chart = e.chart;
      this.updateChart();
    },
    updateChart() {
      console.log('Updating chart...');

      if (!this.chart) {
        console.warn('Chart not initialized yet');
        return;
      }

      let option = {};

      // 根据图表类型生成不同的配置
      switch (this.chartType) {
        case 'bar':
          option = this.getBarOption();
          break;
        case 'line':
          option = this.getLineOption();
          break;
        case 'pie':
          option = this.getPieOption();
          break;
        default:
          option = this.getBarOption();
      }

      console.log('Base option:', JSON.stringify(option));

      // 合并用户自定义配置
      if (this.chartOptions) {
        // 深度合并配置，确保用户配置能够覆盖默认配置
        option = this.deepMerge(option, this.chartOptions);
        console.log('Merged option:', JSON.stringify(option));
      }

      try {
        this.chart.setOption(option);
        console.log('Chart option set successfully');

        // 强制重绘
        setTimeout(() => {
          if (this.chart) {
            this.chart.resize();
          }
        }, 100);
      } catch (error) {
        console.error('Failed to set chart option:', error);
      }
    },

    // 深度合并对象
    deepMerge(target, source) {
      const result = { ...target };

      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
            // 如果是对象，递归合并
            if (typeof result[key] === 'object' && result[key] !== null && !Array.isArray(result[key])) {
              result[key] = this.deepMerge(result[key], source[key]);
            } else {
              result[key] = source[key];
            }
          } else {
            // 如果不是对象，直接覆盖
            result[key] = source[key];
          }
        }
      }

      return result;
    },
    getBarOption() {
      console.log('Getting bar option, chartData:', this.chartData);

      // 提取数据
      const xAxisData = this.chartData.map(item => item.name || '');
      const seriesData = this.chartData.map(item => item.value || 0);

      console.log('xAxisData:', xAxisData);
      console.log('seriesData:', seriesData);

      return {
        color: ['#1976D2'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              fontSize: 10
            }
          }
        ],
        series: [
          {
            name: '数值',
            type: 'bar',
            barWidth: '60%',
            data: seriesData
          }
        ]
      };
    },
    getLineOption() {
      console.log('Getting line option, chartData:', this.chartData);

      // 提取数据
      const xAxisData = this.chartData.map(item => item.day || item.name || '');
      const seriesData = this.chartData.map(item => item.value || 0);

      console.log('xAxisData:', xAxisData);
      console.log('seriesData:', seriesData);

      return {
        color: ['#4CAF50'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            interval: 0,
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [
          {
            name: '数值',
            type: 'line',
            smooth: true,
            data: seriesData,
            areaStyle: {
              opacity: 0.2
            }
          }
        ]
      };
    },
    getPieOption() {
      // 提取数据
      const seriesData = this.chartData.map(item => ({
        name: item.name || '',
        value: item.value || 0
      }));

      return {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 0,
          data: seriesData.map(item => item.name),
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            name: '数据',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: seriesData
          }
        ]
      };
    }
  }
};
</script>

<style>
.ec-chart-container {
  width: 100%;
  height: 300px;
}
</style>
