<template>
  <view class="chartsview">
    <canvas v-if="canvasId" class="charts" :id="canvasId" :canvas-id="canvasId"
      :style="{width: cWidth + 'px', height: cHeight + 'px'}"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
      @tap="tap"></canvas>
    <view v-if="!isShowCharts" class="charts-empty">
      <view class="charts-empty-text">{{emptyText}}</view>
    </view>
  </view>
</template>

<script>
// 引入uCharts库
import uCharts from '@qiun/ucharts/u-charts.js';

export default {
  name: 'blue-column-chart',
  props: {
    canvasId: {
      type: String,
      default: 'bluechartsid'
    },
    chartData: {
      type: Object,
      default: () => ({
        categories: [],
        series: []
      })
    },
    width: {
      type: Number,
      default: 750
    },
    height: {
      type: Number,
      default: 500
    },
    pixelRatio: {
      type: Number,
      default: 1
    },
    background: {
      type: String,
      default: '#FFFFFF'
    },
    animation: {
      type: Boolean,
      default: true
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    enableScroll: {
      type: Boolean,
      default: false
    },
    extra: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      cWidth: 375,
      cHeight: 250,
      isShowCharts: true,
      uchartsOpts: {},
      uchartsInstance: null
    };
  },
  watch: {
    chartData: {
      handler(val) {
        if (val && val.series && val.series.length > 0) {
          this.isShowCharts = true;
          this.drawCharts();
        } else {
          this.isShowCharts = false;
        }
      },
      deep: true
    }
  },
  mounted() {
    this.cWidth = uni.upx2px(this.width);
    this.cHeight = uni.upx2px(this.height);

    // 初始化图表
    if (this.chartData && this.chartData.series && this.chartData.series.length > 0) {
      this.isShowCharts = true;
      this.$nextTick(() => {
        this.drawCharts();
      });
    } else {
      this.isShowCharts = false;
    }
  },
  methods: {
    drawCharts() {
      const ctx = uni.createCanvasContext(this.canvasId, this);

      // 配置项
      const opts = {
        type: 'column',
        context: ctx,
        width: this.cWidth,
        height: this.cHeight,
        pixelRatio: this.pixelRatio,
        animation: this.animation,
        background: this.background,
        categories: this.chartData.categories || [],
        series: this.chartData.series || [],
        enableScroll: this.enableScroll,
        legend: {
          show: true,
          position: 'bottom',
          float: 'center',
          padding: 10
        },
        xAxis: {
          disableGrid: false,
          itemCount: 4,
          labelCount: 4,
          rotateLabel: false,
          disabled: false,
          autoSkipLabel: false,
          axisLine: true,
          boundaryGap: true,
          calibration: true,
          labelWidth: 80,
          interval: 0,
          format: (val) => {
            return val;
          }
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 4,
          data: [
            {
              min: 0
            }
          ]
        },
        extra: {
          ...this.extra,
          column: {
            type: 'group',
            width: 20,
            categoryGap: 20,
            color: '#2979FF' // 强制使用蓝色
          }
        },
        color: ['#2979FF'] // 强制使用蓝色
      };

      // 确保从extra中合并xAxis配置
      if (this.extra && this.extra.xAxis) {
        opts.xAxis = Object.assign({}, opts.xAxis, this.extra.xAxis);
      }

      // 特殊处理：确保所有X轴标签都能显示
      if (this.chartData && this.chartData.categories) {
        // 设置labelCount和itemCount与类别数量一致
        const categoryCount = this.chartData.categories.length;
        console.log('图表类别数量:', categoryCount);
        if (categoryCount > 0) {
          opts.xAxis.labelCount = categoryCount;
          opts.xAxis.itemCount = categoryCount;

          // 根据类别数量调整X轴配置
          opts.xAxis.disabled = false;
          opts.xAxis.autoSkipLabel = false;
          opts.xAxis.boundaryGap = true;

          // 禁用标签旋转，增加标签宽度
          opts.xAxis.rotateLabel = false;
          opts.xAxis.labelWidth = 60;

          // 增加底部内边距，为标签留出足够空间
          if (opts.padding && opts.padding.length >= 4) {
            opts.padding[2] = 60; // 底部内边距
          } else {
            opts.padding = [15, 15, 60, 15];
          }

          // 根据类别数量动态调整柱子宽度和间距
          if (categoryCount <= 2) {
            opts.extra.column.width = 30;
            opts.extra.column.categoryGap = 30;
          } else if (categoryCount <= 4) {
            opts.extra.column.width = 20;
            opts.extra.column.categoryGap = 20;
          } else {
            opts.extra.column.width = Math.min(15, 60 / categoryCount);
            opts.extra.column.categoryGap = 10;
          }
        }
      }

      this.uchartsOpts = opts;

      // 创建图表实例
      this.uchartsInstance = new uCharts(opts);
      this.uchartsInstance.addEventListener('renderComplete', () => {
        this.$emit('complete', {
          type: 'complete',
          complete: true,
          id: this.canvasId
        });
      });
    },

    // 图表点击事件
    tap(e) {
      if (this.uchartsInstance) {
        const currentIndex = this.uchartsInstance.getCurrentDataIndex(e);
        if (currentIndex !== -1) {
          this.$emit('getIndex', {
            type: 'getIndex',
            index: currentIndex,
            id: this.canvasId
          });
        }
      }
    },

    // 图表触摸事件
    touchStart(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.touchLegend(e);
        this.uchartsInstance.scrollStart(e);
      }
    },
    touchMove(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.scroll(e);
      }
    },
    touchEnd(e) {
      if (this.uchartsInstance) {
        this.uchartsInstance.scrollEnd(e);
      }
    }
  }
};
</script>

<style>
.chartsview {
  width: 100%;
  height: 100%;
  position: relative;
}

.charts {
  width: 100% !important;
  height: 100% !important;
  animation: fadeIn 0.5s;
}

.charts-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.charts-empty-text {
  color: #999;
  font-size: 13px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
