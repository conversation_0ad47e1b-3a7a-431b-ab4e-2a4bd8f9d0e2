<template>
  <view class="chart-container">
    <canvas canvas-id="barChart" id="barChart" class="chart-canvas" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"></canvas>
  </view>
</template>

<script>
export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 200
    },
    colors: {
      type: Array,
      default: () => ['#1976D2', '#42A5F5']
    },
    showValues: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    xAxisTitle: {
      type: String,
      default: ''
    },
    yAxisTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ctx: null,
      canvasWidth: 0,
      canvasHeight: 0,
      pixelRatio: 1,
      chartArea: {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      maxValue: 0,
      touchStartX: 0,
      touchStartY: 0
    };
  },
  mounted() {
    this.initChart();
  },
  watch: {
    chartData: {
      handler() {
        this.drawChart();
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      const query = uni.createSelectorQuery().in(this);
      query.select('#barChart')
        .fields({ node: true, size: true })
        .exec(res => {
          if (!res[0]) {
            console.error('Failed to get canvas node');
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          this.ctx = ctx;
          this.canvasWidth = res[0].width;
          this.canvasHeight = res[0].height;
          this.pixelRatio = uni.getSystemInfoSync().pixelRatio;
          
          canvas.width = this.canvasWidth * this.pixelRatio;
          canvas.height = this.canvasHeight * this.pixelRatio;
          ctx.scale(this.pixelRatio, this.pixelRatio);
          
          // 设置图表区域
          const padding = 10;
          const leftPadding = this.yAxisTitle ? 40 : 30;
          const bottomPadding = this.xAxisTitle ? 40 : 30;
          const topPadding = this.title ? 40 : 20;
          
          this.chartArea = {
            left: leftPadding,
            top: topPadding,
            right: this.canvasWidth - padding,
            bottom: this.canvasHeight - bottomPadding,
            width: this.canvasWidth - leftPadding - padding,
            height: this.canvasHeight - topPadding - bottomPadding
          };
          
          this.drawChart();
        });
    },
    drawChart() {
      if (!this.ctx) return;
      
      const ctx = this.ctx;
      const { left, top, right, bottom, width, height } = this.chartArea;
      
      // 清除画布
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      
      // 绘制标题
      if (this.title) {
        ctx.fillStyle = '#333';
        ctx.font = 'bold 14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(this.title, this.canvasWidth / 2, 20);
      }
      
      // 绘制Y轴标题
      if (this.yAxisTitle) {
        ctx.save();
        ctx.fillStyle = '#666';
        ctx.font = '12px sans-serif';
        ctx.translate(15, top + height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.textAlign = 'center';
        ctx.fillText(this.yAxisTitle, 0, 0);
        ctx.restore();
      }
      
      // 绘制X轴标题
      if (this.xAxisTitle) {
        ctx.fillStyle = '#666';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(this.xAxisTitle, left + width / 2, this.canvasHeight - 10);
      }
      
      // 如果没有数据，绘制空图表
      if (!this.chartData || this.chartData.length === 0) {
        this.drawEmptyChart(ctx);
        return;
      }
      
      // 计算最大值
      this.maxValue = Math.max(...this.chartData.map(item => item.value || 0));
      if (this.maxValue === 0) {
        this.maxValue = 100;
      } else {
        // 向上取整到最接近的10或100
        if (this.maxValue <= 10) {
          this.maxValue = Math.ceil(this.maxValue / 5) * 5;
        } else if (this.maxValue <= 100) {
          this.maxValue = Math.ceil(this.maxValue / 10) * 10;
        } else if (this.maxValue <= 1000) {
          this.maxValue = Math.ceil(this.maxValue / 100) * 100;
        } else {
          this.maxValue = Math.ceil(this.maxValue / 1000) * 1000;
        }
      }
      
      // 绘制Y轴
      this.drawYAxis(ctx);
      
      // 绘制X轴
      this.drawXAxis(ctx);
      
      // 绘制柱状图
      this.drawBars(ctx);
    },
    drawEmptyChart(ctx) {
      const { left, top, right, bottom, width, height } = this.chartArea;
      
      // 绘制Y轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, top);
      ctx.lineTo(left, bottom);
      ctx.stroke();
      
      // 绘制X轴
      ctx.beginPath();
      ctx.moveTo(left, bottom);
      ctx.lineTo(right, bottom);
      ctx.stroke();
      
      // 绘制网格线
      const yStep = height / 4;
      for (let i = 0; i <= 4; i++) {
        const y = bottom - i * yStep;
        
        // 绘制水平网格线
        ctx.beginPath();
        ctx.strokeStyle = '#f0f0f0';
        ctx.moveTo(left, y);
        ctx.lineTo(right, y);
        ctx.stroke();
        
        // 绘制Y轴刻度
        ctx.fillStyle = '#999';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText((i * 25).toString(), left - 5, y + 3);
      }
      
      // 绘制"暂无数据"文本
      ctx.fillStyle = '#999';
      ctx.font = '14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('暂无数据', left + width / 2, top + height / 2);
    },
    drawYAxis(ctx) {
      const { left, top, bottom, height } = this.chartArea;
      
      // 绘制Y轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, top);
      ctx.lineTo(left, bottom);
      ctx.stroke();
      
      // 绘制网格线和刻度
      const yStep = height / 4;
      for (let i = 0; i <= 4; i++) {
        const y = bottom - i * yStep;
        const value = (i * this.maxValue / 4).toFixed(0);
        
        // 绘制水平网格线
        ctx.beginPath();
        ctx.strokeStyle = '#f0f0f0';
        ctx.moveTo(left, y);
        ctx.lineTo(left + this.chartArea.width, y);
        ctx.stroke();
        
        // 绘制Y轴刻度
        ctx.fillStyle = '#999';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText(value, left - 5, y + 3);
      }
    },
    drawXAxis(ctx) {
      const { left, bottom, width } = this.chartArea;
      
      // 绘制X轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, bottom);
      ctx.lineTo(left + width, bottom);
      ctx.stroke();
      
      // 绘制X轴刻度和标签
      const barCount = this.chartData.length;
      const barWidth = width / (barCount * 2 + 1);
      
      for (let i = 0; i < barCount; i++) {
        const x = left + barWidth * (i * 2 + 1.5);
        const label = this.chartData[i].name || '';
        
        // 绘制X轴标签
        ctx.fillStyle = '#666';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'center';
        
        // 如果标签太长，截断并添加省略号
        let displayLabel = label;
        if (ctx.measureText(label).width > barWidth * 2) {
          let tempLabel = label;
          while (ctx.measureText(tempLabel + '...').width > barWidth * 2 && tempLabel.length > 1) {
            tempLabel = tempLabel.slice(0, -1);
          }
          displayLabel = tempLabel + '...';
        }
        
        ctx.fillText(displayLabel, x, bottom + 15);
      }
    },
    drawBars(ctx) {
      const { left, bottom, width, height } = this.chartArea;
      
      const barCount = this.chartData.length;
      const barWidth = width / (barCount * 2 + 1);
      
      for (let i = 0; i < barCount; i++) {
        const value = this.chartData[i].value || 0;
        const x = left + barWidth * (i * 2 + 1);
        const barHeight = (value / this.maxValue) * height;
        const y = bottom - barHeight;
        
        // 绘制柱子
        ctx.fillStyle = this.colors[i % this.colors.length];
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // 绘制数值
        if (this.showValues && value > 0) {
          ctx.fillStyle = '#333';
          ctx.font = '10px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(value.toFixed(1), x + barWidth / 2, y - 5);
        }
      }
    },
    touchStart(e) {
      const touch = e.touches[0];
      this.touchStartX = touch.x;
      this.touchStartY = touch.y;
    },
    touchMove(e) {
      // 可以实现拖动等交互
    },
    touchEnd(e) {
      const touch = e.changedTouches[0];
      const endX = touch.x;
      const endY = touch.y;
      
      // 检测点击了哪个柱子
      if (Math.abs(endX - this.touchStartX) < 10 && Math.abs(endY - this.touchStartY) < 10) {
        const { left, bottom, width, height } = this.chartArea;
        const barCount = this.chartData.length;
        const barWidth = width / (barCount * 2 + 1);
        
        for (let i = 0; i < barCount; i++) {
          const x = left + barWidth * (i * 2 + 1);
          const value = this.chartData[i].value || 0;
          const barHeight = (value / this.maxValue) * height;
          const y = bottom - barHeight;
          
          if (endX >= x && endX <= x + barWidth && endY >= y && endY <= bottom) {
            // 点击了第i个柱子
            this.$emit('bar-click', this.chartData[i], i);
            break;
          }
        }
      }
    }
  }
};
</script>

<style>
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
