<template>
  <view class="chart-container">
    <canvas canvas-id="lineChart" id="lineChart" class="chart-canvas" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"></canvas>
  </view>
</template>

<script>
export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 200
    },
    lineColor: {
      type: String,
      default: '#4CAF50'
    },
    fillColor: {
      type: String,
      default: 'rgba(76, 175, 80, 0.1)'
    },
    pointColor: {
      type: String,
      default: '#4CAF50'
    },
    showValues: {
      type: Boolean,
      default: true
    },
    smooth: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    xAxisTitle: {
      type: String,
      default: ''
    },
    yAxisTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ctx: null,
      canvasWidth: 0,
      canvasHeight: 0,
      pixelRatio: 1,
      chartArea: {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      maxValue: 0,
      touchStartX: 0,
      touchStartY: 0,
      activePointIndex: -1
    };
  },
  mounted() {
    this.initChart();
  },
  watch: {
    chartData: {
      handler() {
        this.drawChart();
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      const query = uni.createSelectorQuery().in(this);
      query.select('#lineChart')
        .fields({ node: true, size: true })
        .exec(res => {
          if (!res[0]) {
            console.error('Failed to get canvas node');
            return;
          }
          
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          this.ctx = ctx;
          this.canvasWidth = res[0].width;
          this.canvasHeight = res[0].height;
          this.pixelRatio = uni.getSystemInfoSync().pixelRatio;
          
          canvas.width = this.canvasWidth * this.pixelRatio;
          canvas.height = this.canvasHeight * this.pixelRatio;
          ctx.scale(this.pixelRatio, this.pixelRatio);
          
          // 设置图表区域
          const padding = 10;
          const leftPadding = this.yAxisTitle ? 40 : 30;
          const bottomPadding = this.xAxisTitle ? 40 : 30;
          const topPadding = this.title ? 40 : 20;
          
          this.chartArea = {
            left: leftPadding,
            top: topPadding,
            right: this.canvasWidth - padding,
            bottom: this.canvasHeight - bottomPadding,
            width: this.canvasWidth - leftPadding - padding,
            height: this.canvasHeight - topPadding - bottomPadding
          };
          
          this.drawChart();
        });
    },
    drawChart() {
      if (!this.ctx) return;
      
      const ctx = this.ctx;
      const { left, top, right, bottom, width, height } = this.chartArea;
      
      // 清除画布
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      
      // 绘制标题
      if (this.title) {
        ctx.fillStyle = '#333';
        ctx.font = 'bold 14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(this.title, this.canvasWidth / 2, 20);
      }
      
      // 绘制Y轴标题
      if (this.yAxisTitle) {
        ctx.save();
        ctx.fillStyle = '#666';
        ctx.font = '12px sans-serif';
        ctx.translate(15, top + height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.textAlign = 'center';
        ctx.fillText(this.yAxisTitle, 0, 0);
        ctx.restore();
      }
      
      // 绘制X轴标题
      if (this.xAxisTitle) {
        ctx.fillStyle = '#666';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(this.xAxisTitle, left + width / 2, this.canvasHeight - 10);
      }
      
      // 如果没有数据，绘制空图表
      if (!this.chartData || this.chartData.length === 0) {
        this.drawEmptyChart(ctx);
        return;
      }
      
      // 计算最大值
      this.maxValue = Math.max(...this.chartData.map(item => item.value || 0));
      if (this.maxValue === 0) {
        this.maxValue = 100;
      } else {
        // 向上取整到最接近的10或100
        if (this.maxValue <= 10) {
          this.maxValue = Math.ceil(this.maxValue / 5) * 5;
        } else if (this.maxValue <= 100) {
          this.maxValue = Math.ceil(this.maxValue / 10) * 10;
        } else if (this.maxValue <= 1000) {
          this.maxValue = Math.ceil(this.maxValue / 100) * 100;
        } else {
          this.maxValue = Math.ceil(this.maxValue / 1000) * 1000;
        }
      }
      
      // 绘制Y轴
      this.drawYAxis(ctx);
      
      // 绘制X轴
      this.drawXAxis(ctx);
      
      // 绘制折线图
      this.drawLine(ctx);
    },
    drawEmptyChart(ctx) {
      const { left, top, right, bottom, width, height } = this.chartArea;
      
      // 绘制Y轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, top);
      ctx.lineTo(left, bottom);
      ctx.stroke();
      
      // 绘制X轴
      ctx.beginPath();
      ctx.moveTo(left, bottom);
      ctx.lineTo(right, bottom);
      ctx.stroke();
      
      // 绘制网格线
      const yStep = height / 4;
      for (let i = 0; i <= 4; i++) {
        const y = bottom - i * yStep;
        
        // 绘制水平网格线
        ctx.beginPath();
        ctx.strokeStyle = '#f0f0f0';
        ctx.moveTo(left, y);
        ctx.lineTo(right, y);
        ctx.stroke();
        
        // 绘制Y轴刻度
        ctx.fillStyle = '#999';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText((i * 25).toString(), left - 5, y + 3);
      }
      
      // 绘制"暂无数据"文本
      ctx.fillStyle = '#999';
      ctx.font = '14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('暂无数据', left + width / 2, top + height / 2);
    },
    drawYAxis(ctx) {
      const { left, top, bottom, height } = this.chartArea;
      
      // 绘制Y轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, top);
      ctx.lineTo(left, bottom);
      ctx.stroke();
      
      // 绘制网格线和刻度
      const yStep = height / 4;
      for (let i = 0; i <= 4; i++) {
        const y = bottom - i * yStep;
        const value = (i * this.maxValue / 4).toFixed(0);
        
        // 绘制水平网格线
        ctx.beginPath();
        ctx.strokeStyle = '#f0f0f0';
        ctx.moveTo(left, y);
        ctx.lineTo(left + this.chartArea.width, y);
        ctx.stroke();
        
        // 绘制Y轴刻度
        ctx.fillStyle = '#999';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText(value, left - 5, y + 3);
      }
    },
    drawXAxis(ctx) {
      const { left, bottom, width } = this.chartArea;
      
      // 绘制X轴
      ctx.beginPath();
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.moveTo(left, bottom);
      ctx.lineTo(left + width, bottom);
      ctx.stroke();
      
      // 绘制X轴刻度和标签
      const pointCount = this.chartData.length;
      const xStep = width / (pointCount - 1);
      
      for (let i = 0; i < pointCount; i++) {
        const x = left + xStep * i;
        const label = this.chartData[i].day || '';
        
        // 绘制X轴标签
        ctx.fillStyle = '#666';
        ctx.font = '10px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(label, x, bottom + 15);
      }
    },
    drawLine(ctx) {
      const { left, bottom, width, height } = this.chartArea;
      
      const pointCount = this.chartData.length;
      if (pointCount < 2) return;
      
      const xStep = width / (pointCount - 1);
      
      // 计算点的坐标
      const points = this.chartData.map((item, index) => {
        const x = left + xStep * index;
        const value = item.value || 0;
        const y = bottom - (value / this.maxValue) * height;
        return { x, y, value, label: item.day, date: item.date };
      });
      
      // 绘制填充区域
      ctx.beginPath();
      ctx.moveTo(points[0].x, bottom);
      
      if (this.smooth) {
        // 绘制平滑曲线
        ctx.lineTo(points[0].x, points[0].y);
        
        for (let i = 0; i < points.length - 1; i++) {
          const xc = (points[i].x + points[i + 1].x) / 2;
          const yc = (points[i].y + points[i + 1].y) / 2;
          ctx.quadraticCurveTo(points[i].x, points[i].y, xc, yc);
        }
        
        ctx.quadraticCurveTo(
          points[pointCount - 2].x,
          points[pointCount - 2].y,
          points[pointCount - 1].x,
          points[pointCount - 1].y
        );
      } else {
        // 绘制折线
        for (let i = 0; i < points.length; i++) {
          if (i === 0) {
            ctx.lineTo(points[i].x, points[i].y);
          } else {
            ctx.lineTo(points[i].x, points[i].y);
          }
        }
      }
      
      ctx.lineTo(points[pointCount - 1].x, bottom);
      ctx.closePath();
      ctx.fillStyle = this.fillColor;
      ctx.fill();
      
      // 绘制线条
      ctx.beginPath();
      
      if (this.smooth) {
        // 绘制平滑曲线
        ctx.moveTo(points[0].x, points[0].y);
        
        for (let i = 0; i < points.length - 1; i++) {
          const xc = (points[i].x + points[i + 1].x) / 2;
          const yc = (points[i].y + points[i + 1].y) / 2;
          ctx.quadraticCurveTo(points[i].x, points[i].y, xc, yc);
        }
        
        ctx.quadraticCurveTo(
          points[pointCount - 2].x,
          points[pointCount - 2].y,
          points[pointCount - 1].x,
          points[pointCount - 1].y
        );
      } else {
        // 绘制折线
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
          ctx.lineTo(points[i].x, points[i].y);
        }
      }
      
      ctx.strokeStyle = this.lineColor;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制数据点和数值
      for (let i = 0; i < points.length; i++) {
        const point = points[i];
        
        // 绘制数据点
        ctx.beginPath();
        ctx.arc(point.x, point.y, i === this.activePointIndex ? 5 : 3, 0, Math.PI * 2);
        ctx.fillStyle = this.pointColor;
        ctx.fill();
        
        // 绘制数值
        if (this.showValues || i === this.activePointIndex) {
          ctx.fillStyle = '#333';
          ctx.font = '10px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(point.value.toFixed(1), point.x, point.y - 10);
          
          if (i === this.activePointIndex && point.date) {
            ctx.fillText(point.date, point.x, point.y - 25);
          }
        }
      }
    },
    touchStart(e) {
      const touch = e.touches[0];
      this.touchStartX = touch.x;
      this.touchStartY = touch.y;
    },
    touchMove(e) {
      // 可以实现拖动等交互
    },
    touchEnd(e) {
      const touch = e.changedTouches[0];
      const endX = touch.x;
      const endY = touch.y;
      
      // 检测点击了哪个点
      if (Math.abs(endX - this.touchStartX) < 10 && Math.abs(endY - this.touchStartY) < 10) {
        const { left, width } = this.chartArea;
        const pointCount = this.chartData.length;
        const xStep = width / (pointCount - 1);
        
        for (let i = 0; i < pointCount; i++) {
          const x = left + xStep * i;
          
          if (Math.abs(endX - x) < 15) {
            // 点击了第i个点
            this.activePointIndex = i;
            this.drawChart();
            this.$emit('point-click', this.chartData[i], i);
            break;
          }
        }
      }
    }
  }
};
</script>

<style>
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
